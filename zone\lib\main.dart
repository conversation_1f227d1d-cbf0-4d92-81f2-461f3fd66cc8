import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:zone/screens/onboarding_screen.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'firebase_options.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/customer_ui.dart';
import 'screens/profile_customer.dart';
import 'screens/map_screen.dart';
import 'screens/messages_list_screen.dart';
import 'screens/favorites_screen.dart';
import 'screens/viewed_properties_screen.dart';
import 'seller/landlord/dashboard_screen.dart';
import 'firebase_auth.dart';
import 'admin/admin_dashboard.dart';
import 'utils/map_utils.dart'; // Import the map utilities

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Google Maps
  await MapUtils.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Property Edge',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      routes: {
        '/onboarding': (context) => const OnboardingScreen(),
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/home': (context) => const CustomerHomeScreen(),
        '/profile': (context) => const ProfileCustomerScreen(),
        '/dashboard': (context) => const DashboardScreen(),
        '/map': (context) => const PropertyMapScreen(),
        '/admin': (context) => const AdminDashboard(),
        '/messages': (context) => const MessagesListScreen(),
        '/favorite': (context) => const FavoritesScreen(),
        '/viewed_properties': (context) => const ViewedPropertiesScreen(),
      },
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final Auth _auth = Auth();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: _auth.authStateChanges,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.active) {
          final user = snapshot.data;
          if (user == null) {
            return const LoginScreen(); // Not authenticated
          } else {
            return FutureBuilder<String?>(
              future: _auth.getUserRole(),
              builder: (context, roleSnapshot) {
                if (roleSnapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  );
                }

                final userRole = roleSnapshot.data;

                // Check if user is admin
                if (userRole == 'admin') {
                  return const AdminDashboard(); // For admin users
                } else if (userRole == 'Seller' || userRole == 'Landlord') {
                  return const DashboardScreen(); // For sellers/landlords
                } else {
                  return const CustomerHomeScreen(); // For buyers/tenants
                }
              },
            );
          }
        }

        // While connecting show loading
        return const Scaffold(body: Center(child: CircularProgressIndicator()));
      },
    );
  }
}
