import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../firebase_auth.dart';
import 'package:logger/logger.dart';
import 'property_detail_screen.dart';

class ViewedPropertiesScreen extends StatefulWidget {
  const ViewedPropertiesScreen({super.key});

  @override
  State<ViewedPropertiesScreen> createState() => _ViewedPropertiesScreenState();
}

class _ViewedPropertiesScreenState extends State<ViewedPropertiesScreen> {
  final Auth _auth = Auth();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();
  bool _isLoading = true;
  List<Map<String, dynamic>> _viewedProperties = [];
  int _selectedIndex = 3; // Set to 3 to highlight Profile tab

  @override
  void initState() {
    super.initState();
    _loadViewedProperties();
  }

  Future<void> _loadViewedProperties() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Get user's viewed properties
      final viewsQuery =
          await _firestore
              .collection('views')
              .where('userId', isEqualTo: userId)
              .orderBy('viewedAt', descending: true)
              .get();

      // Create a list to hold complete property data
      final List<Map<String, dynamic>> properties = [];

      // For each view, get the property details
      for (final doc in viewsQuery.docs) {
        final String propertyId = doc['propertyId'] as String;
        final Timestamp viewedAt = doc['viewedAt'] as Timestamp;

        try {
          final propertyDoc =
              await _firestore.collection('properties').doc(propertyId).get();

          if (propertyDoc.exists) {
            final propertyData = propertyDoc.data() as Map<String, dynamic>;
            properties.add({
              ...propertyData,
              'id': propertyDoc.id,
              'viewId': doc.id,
              'viewedAt': viewedAt,
            });
          }
        } catch (e) {
          _logger.e('Error fetching property $propertyId: $e');
        }
      }

      if (mounted) {
        setState(() {
          _viewedProperties = properties;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading viewed properties: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Viewing History'), elevation: 0),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _viewedProperties.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.visibility_outlined,
                      size: 80,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No viewing history',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Properties you view will appear here',
                      style: TextStyle(fontSize: 16, color: Colors.grey[500]),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _viewedProperties.length,
                itemBuilder: (context, index) {
                  final property = _viewedProperties[index];
                  final String title = property['title'] ?? 'No Title';
                  final String location = property['location'] ?? 'No Location';
                  final List<dynamic> images = property['images'] ?? [];
                  final Timestamp viewedAt = property['viewedAt'];
                  final DateTime viewedDate = viewedAt.toDate();
                  final String formattedDate = _formatDate(viewedDate);

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    PropertyDetailScreen(property: property),
                          ),
                        );
                      },
                      child: Row(
                        children: [
                          // Property image
                          ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              bottomLeft: Radius.circular(12),
                            ),
                            child: SizedBox(
                              width: 120,
                              height: 120,
                              child:
                                  images.isNotEmpty
                                      ? Image.network(
                                        images[0],
                                        fit: BoxFit.cover,
                                      )
                                      : Container(
                                        color: Colors.grey[300],
                                        child: const Icon(
                                          Icons.home,
                                          size: 50,
                                          color: Colors.white,
                                        ),
                                      ),
                            ),
                          ),

                          // Property details
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    title,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.location_on,
                                        size: 14,
                                        color: Colors.grey,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          location,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey[600],
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.history,
                                        size: 14,
                                        color: Colors.grey[600],
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Viewed: $formattedDate',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  // Format the viewed date
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Bottom navigation bar
  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      onTap: (index) {
        if (index != _selectedIndex) {
          setState(() {
            _selectedIndex = index;
          });

          if (index == 0) {
            Navigator.pushReplacementNamed(context, '/home');
          } else if (index == 1) {
            Navigator.pushReplacementNamed(context, '/messages');
          } else if (index == 2) {
            Navigator.pushReplacementNamed(context, '/favorite');
          } else if (index == 3) {
            Navigator.pushReplacementNamed(context, '/profile');
          }
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: 'Messages',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.favorite_border),
          label: 'Saved',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
      ],
    );
  }
}
