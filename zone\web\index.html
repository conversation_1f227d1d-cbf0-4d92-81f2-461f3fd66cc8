<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="zone">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <!-- Google Maps API - Load first so it's available when Flutter initializes -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyChzyEWjJMQeHftfAq0yjg7LybpJtAQLvY&libraries=places"></script>
  <script src="flutter_maps_init.js"></script>

  <title>zone</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>

  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>
  <script>
    var firebaseConfig = {
      apiKey: "AIzaSyCO0FsF_USySrv554KaKUrEWHe-V16FXuw",
      projectId: "edge-property",
      storageBucket: "edge-property.firebasestorage.app",
      messagingSenderId: "1079911448359",
      appId: "1:1079911448359:web:06cc06ff608ffc5388b302"
    };
    firebase.initializeApp(firebaseConfig);
  </script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      // Check if Google Maps API is loaded
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not available. Some features may not work properly.');
      }
      
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
