import 'package:flutter/material.dart';
import 'settings_screen.dart'; // Import the settings screen
import '../add_listing.dart'; // Import the add listing screen
import '../seller_listings_screen.dart'; // Import the seller listings screen
import '../seller_inquiries_screen.dart'; // Import the seller inquiries screen
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  int _activeListings = 0;
  int _rentListings = 0;
  int _saleListings = 0;
  int _newInquiries = 0;
  int _unreadInquiries = 0;
  bool _isLoading = true;
  List<Map<String, dynamic>> _recentListings = [];

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      // Get active listings count
      final listingsQuery =
          await _firestore
              .collection('properties')
              .where('userId', isEqualTo: userId)
              .where('isVerified', isEqualTo: true)
              .where('verificationStatus', isEqualTo: 'approved')
              .get();

      int rentCount = 0;
      int saleCount = 0;

      for (var doc in listingsQuery.docs) {
        if (doc['listingType'] == 'rent') {
          rentCount++;
        } else if (doc['listingType'] == 'sale') {
          saleCount++;
        }
      }

      // Get inquiries count
      final conversationsQuery =
          await _firestore
              .collection('conversations')
              .where('agentId', isEqualTo: userId)
              .get();

      int unreadCount = 0;
      for (var doc in conversationsQuery.docs) {
        unreadCount += (doc['unreadCount'] as int? ?? 0);
      }

      // Get recent listings
      final recentListingsQuery =
          await _firestore
              .collection('properties')
              .where('userId', isEqualTo: userId)
              .where('isVerified', isEqualTo: true)
              .where('verificationStatus', isEqualTo: 'approved')
              .orderBy('createdAt', descending: true)
              .limit(2)
              .get();

      final listings =
          recentListingsQuery.docs.map((doc) {
            final data = doc.data();
            return {
              ...data,
              'id': doc.id,
              'daysListed':
                  DateTime.now()
                      .difference((data['createdAt'] as Timestamp).toDate())
                      .inDays,
            };
          }).toList();

      if (mounted) {
        setState(() {
          _activeListings = listingsQuery.docs.length;
          _rentListings = rentCount;
          _saleListings = saleCount;
          _newInquiries = conversationsQuery.docs.length;
          _unreadInquiries = unreadCount;
          _recentListings = listings;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen information for responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700 || screenWidth < 360;

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dashboard',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 24 : 28,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  'Overview of your property listings and activities',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 14 : 16,
                    color: const Color(0xFF666666),
                  ),
                ),
                SizedBox(height: isSmallScreen ? 16.0 : 24.0),
                _buildStatsGrid(context),
                SizedBox(height: isSmallScreen ? 16.0 : 24.0),
                _buildRecentListings(context),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  Widget _buildStatsGrid(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : Container(
          padding: const EdgeInsets.all(4.0),
          child: GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 8.0,
            mainAxisSpacing: 8.0,
            childAspectRatio:
                screenWidth <= 360 ? 1.1 : (screenWidth <= 600 ? 1.3 : 1.5),
            children: [
              _buildStatCard(
                title: 'Active Listings',
                value: _activeListings.toString(),
                subtitle: '$_rentListings for rent, $_saleListings for sale',
                icon: Icons.description_outlined,
              ),
              _buildStatCard(
                title: 'New Inquiries',
                value: _newInquiries.toString(),
                subtitle: '$_unreadInquiries unread messages',
                icon: Icons.chat_bubble_outline,
              ),
            ],
          ),
        );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
  }) {
    // Get screen information for responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(icon, color: Colors.grey, size: isSmallScreen ? 18 : 22),
              ],
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),
            Text(
              value,
              style: TextStyle(
                fontSize: isSmallScreen ? 24 : 28,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(height: isSmallScreen ? 2 : 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: isSmallScreen ? 12 : 14,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentListings(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Listings',
          style: TextStyle(
            fontSize: isSmallScreen ? 18 : 22,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
        ),
        Text(
          'Your most recently added properties',
          style: TextStyle(
            fontSize: isSmallScreen ? 14 : 16,
            color: const Color(0xFF666666),
          ),
        ),
        SizedBox(height: isSmallScreen ? 12 : 16),

        if (_isLoading)
          const Center(child: CircularProgressIndicator())
        else if (_recentListings.isEmpty)
          const Center(child: Text('No listings found'))
        else if (screenWidth > 600)
          // Tablet layout
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:
                _recentListings.map((property) {
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: _buildPropertyCard(
                        title: property['title'] ?? 'No Title',
                        address: property['location'] ?? 'No Location',
                        price:
                            '₱${(property['price'] ?? 0).toString()}${property['listingType'] == 'rent' ? '/month' : ''}',
                        type: property['propertyType'] ?? 'Property',
                        status:
                            property['listingType'] == 'rent'
                                ? 'For Rent'
                                : 'For Sale',
                        statusColor:
                            property['listingType'] == 'rent'
                                ? Colors.green
                                : Colors.orange,
                        views: property['views'] ?? 0,
                        inquiries: property['inquiries'] ?? 0,
                        isAvailable: property['isAvailable'] ?? false,
                        daysListed: property['daysListed'],
                        imageUrl:
                            property['images'] != null &&
                                    (property['images'] as List).isNotEmpty
                                ? property['images'][0]
                                : null,
                      ),
                    ),
                  );
                }).toList(),
          )
        else
          // Mobile layout
          Column(
            children:
                _recentListings.map((property) {
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom: isSmallScreen ? 12.0 : 16.0,
                    ),
                    child: _buildPropertyCard(
                      title: property['title'] ?? 'No Title',
                      address: property['location'] ?? 'No Location',
                      price:
                          '₱${(property['price'] ?? 0).toString()}${property['listingType'] == 'rent' ? '/month' : ''}',
                      type: property['propertyType'] ?? 'Property',
                      status:
                          property['listingType'] == 'rent'
                              ? 'For Rent'
                              : 'For Sale',
                      statusColor:
                          property['listingType'] == 'rent'
                              ? Colors.green
                              : Colors.orange,
                      views: property['views'] ?? 0,
                      inquiries: property['inquiries'] ?? 0,
                      isAvailable: property['isAvailable'] ?? false,
                      daysListed: property['daysListed'],
                      imageUrl:
                          property['images'] != null &&
                                  (property['images'] as List).isNotEmpty
                              ? property['images'][0]
                              : null,
                    ),
                  );
                }).toList(),
          ),

        SizedBox(height: isSmallScreen ? 12 : 16),
        Center(
          child: TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SellerListingsScreen(),
                ),
              );
            },
            child: Text(
              'View All Listings',
              style: TextStyle(
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyCard({
    required String title,
    required String address,
    required String price,
    required String type,
    required String status,
    required Color statusColor,
    required int views,
    required int inquiries,
    required bool isAvailable,
    int? daysListed,
    String? imageUrl,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                image:
                    imageUrl != null
                        ? DecorationImage(
                          image: NetworkImage(imageUrl),
                          fit: BoxFit.cover,
                        )
                        : null,
              ),
              child:
                  imageUrl == null
                      ? Center(
                        child: Icon(
                          Icons.image,
                          size: 40,
                          color: Colors.grey[400],
                        ),
                      )
                      : null,
            ),
          ),
          Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF333333),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(Icons.more_horiz),
                  ],
                ),
                Text(
                  address,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 14,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isSmallScreen ? 8 : 12),
                _buildWrappedTags(type, status, isAvailable),
                SizedBox(height: isSmallScreen ? 8 : 12),
                Text(
                  price,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                SizedBox(height: isSmallScreen ? 8 : 12),
                Wrap(
                  spacing: isSmallScreen ? 12 : 16,
                  runSpacing: 8,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.visibility_outlined,
                          size: isSmallScreen ? 16 : 18,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: isSmallScreen ? 2 : 4),
                        Text(
                          '$views views',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 12 : 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: isSmallScreen ? 16 : 18,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: isSmallScreen ? 2 : 4),
                        Text(
                          '$inquiries inquiries',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 12 : 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                if (daysListed != null) ...[
                  SizedBox(height: isSmallScreen ? 6 : 8),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.description_outlined,
                        size: isSmallScreen ? 16 : 18,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: isSmallScreen ? 2 : 4),
                      Text(
                        'Listed $daysListed days ago',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 12 : 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWrappedTags(String type, String status, bool isAvailable) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Wrap(
      spacing: isSmallScreen ? 6 : 8,
      runSpacing: isSmallScreen ? 6 : 8,
      children: [
        _buildTag(type, Colors.grey[200]!),
        _buildTag(status, Colors.grey[200]!),
        _buildTag(
          isAvailable ? 'Available' : 'Pending',
          isAvailable ? Colors.green : Colors.orange,
          textColor: Colors.white,
        ),
      ],
    );
  }

  Widget _buildTag(
    String text,
    Color backgroundColor, {
    Color textColor = Colors.black87,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 8 : 12,
        vertical: isSmallScreen ? 4 : 6,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: isSmallScreen ? 12 : 14, color: textColor),
      ),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade300, width: 1)),
      ),
      height: isSmallScreen ? 50 : 54,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildSimpleNavItem(Icons.home_outlined, 'Home', true, () {}),
          _buildSimpleNavItem(Icons.edit_document, 'Listings', false, () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SellerListingsScreen(),
              ),
            );
          }),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 4.0 : 8.0,
            ),
            child: FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddListingScreen(),
                  ),
                );
              },
              backgroundColor: Colors.black,
              elevation: 2,
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: isSmallScreen ? 22 : 24,
              ),
            ),
          ),
          _buildSimpleNavItem(
            Icons.chat_bubble_outline,
            'Inquiries',
            false,
            () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SellerInquiriesScreen(),
                ),
              );
            },
          ),
          _buildSimpleNavItem(Icons.settings_outlined, 'Settings', false, () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SettingsScreen()),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSimpleNavItem(
    IconData icon,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isSelected ? Colors.black : Colors.grey,
            size: isSmallScreen ? 20 : 22,
          ),
          SizedBox(height: isSmallScreen ? 2 : 4),
          Text(
            label,
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 12,
              color: isSelected ? Colors.black : Colors.grey,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
