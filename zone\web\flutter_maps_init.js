// This script initializes the Google Maps library for Flutter web
// It ensures that the Flutter web app can properly initialize the Google Maps plugin

function initializeGoogleMaps() {
  if (window.google && window.google.maps) {
    console.log('Google Maps API loaded successfully');
    return;
  } else {
    console.error('Google Maps API failed to load. Check your API key and script inclusion.');
  }
}

// Function to check if Google Maps exists - accessible from Dart
function checkGoogleMapsExists() {
  return !!(window.google && window.google.maps);
}

// Call the initialization function when the page loads
window.addEventListener('load', initializeGoogleMaps); 