rules_version = '2';

// Storage rules for profile images and property images
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload profile images
    match /profile_images/{userId}.jpg {
      // Anyone can view profile images
      allow read: if true;
      // Only the user can upload their own profile image
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to upload panoramic images to their directory
    match /panoramas/{userId}/{fileName} {
      // Anyone can view panoramic images
      allow read: if true;
      // Only the user can upload to their own folder
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to upload regular images to their directory
    match /listings/{userId}/{fileName} {
      // Anyone can view listing images
      allow read: if true;
      // Only the user can upload to their own folder
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
} 