{"indexes": [{"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "verificationStatus", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isVerified", "order": "ASCENDING"}, {"fieldPath": "verificationStatus", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "propertyType", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "saved", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "savedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "recipientId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}