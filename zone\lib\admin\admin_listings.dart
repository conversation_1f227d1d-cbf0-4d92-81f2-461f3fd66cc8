import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/web.dart';
import '../models/firestore_collections.dart';
import 'dart:convert';

class AdminListings extends StatefulWidget {
  const AdminListings({super.key});

  @override
  State<AdminListings> createState() => _AdminListingsState();
}

class _AdminListingsState extends State<AdminListings> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = 'All';
  bool _isLoading = false;
  List<DocumentSnapshot> _listings = [];
  List<DocumentSnapshot> _filteredListings = [];

  @override
  void initState() {
    super.initState();
    _loadListings();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadListings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final QuerySnapshot snapshot =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .orderBy('createdAt', descending: true)
              .get();

      if (mounted) {
        setState(() {
          _listings = snapshot.docs;
          _filteredListings = _listings;
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger().e('Error loading listings: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterListings() {
    setState(() {
      if (_selectedStatus == 'All') {
        _filteredListings =
            _listings.where((doc) {
              final data = doc.data() as Map<String, dynamic>;
              final title = data['title'] as String? ?? '';
              final description = data['description'] as String? ?? '';
              final location = data['location'] as String? ?? '';

              final searchQuery = _searchController.text.toLowerCase();

              return title.toLowerCase().contains(searchQuery) ||
                  description.toLowerCase().contains(searchQuery) ||
                  location.toLowerCase().contains(searchQuery);
            }).toList();
      } else {
        _filteredListings =
            _listings.where((doc) {
              final data = doc.data() as Map<String, dynamic>;
              final status = data['status'] as String? ?? '';
              final title = data['title'] as String? ?? '';
              final description = data['description'] as String? ?? '';
              final location = data['location'] as String? ?? '';

              final searchQuery = _searchController.text.toLowerCase();

              return status.toLowerCase() == _selectedStatus.toLowerCase() &&
                  (title.toLowerCase().contains(searchQuery) ||
                      description.toLowerCase().contains(searchQuery) ||
                      location.toLowerCase().contains(searchQuery));
            }).toList();
      }
    });
  }

  Future<void> _deleteProperty(String propertyId) async {
    try {
      await FirebaseFirestore.instance
          .collection(FirestoreCollections.properties)
          .doc(propertyId)
          .delete();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Property deleted successfully')),
        );
        _loadListings();
      }
    } catch (e) {
      Logger().e('Error deleting property: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error deleting property: $e')));
      }
    }
  }

  Future<void> _togglePropertyStatus(
    String propertyId,
    String currentStatus,
  ) async {
    final String newStatus =
        currentStatus == 'available' ? 'unavailable' : 'available';

    try {
      await FirebaseFirestore.instance
          .collection(FirestoreCollections.properties)
          .doc(propertyId)
          .update({'status': newStatus});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Property status updated to $newStatus')),
        );
        _loadListings();
      }
    } catch (e) {
      Logger().e('Error updating property status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating property status: $e')),
        );
      }
    }
  }

  void _showDeleteConfirmationDialog(String propertyId, String propertyTitle) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Property'),
            content: Text('Are you sure you want to delete "$propertyTitle"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteProperty(propertyId);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Widget _buildImageWidget(dynamic imageData) {
    if (imageData == null) {
      return Icon(Icons.home, color: Colors.white);
    }

    if (imageData is String) {
      // Check if it's a URL (starts with http or https)
      if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
        return Image.network(
          imageData,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.broken_image, color: Colors.white);
          },
        );
      }

      // Try to decode as base64
      try {
        return Image.memory(
          base64Decode(imageData),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.broken_image, color: Colors.white);
          },
        );
      } catch (e) {
        return Icon(Icons.broken_image, color: Colors.white);
      }
    }

    return Icon(Icons.home, color: Colors.white);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'Property Listings',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions:
            MediaQuery.of(context).size.width > 600
                ? [
                  IconButton(
                    icon: Icon(Icons.dark_mode, color: Colors.white),
                    onPressed: () {},
                  ),
                  Stack(
                    children: [
                      IconButton(
                        icon: Icon(Icons.notifications, color: Colors.white),
                        onPressed: () {},
                      ),
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '3',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 16,
                    child: Icon(Icons.person, color: Colors.grey),
                  ),
                  SizedBox(width: 16),
                ]
                : [],
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // Different layouts based on width
          final isTabletOrLarger = constraints.maxWidth > 600;
          final isMobile = constraints.maxWidth <= 600;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isTabletOrLarger)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Property Listings',
                    style: TextStyle(
                      fontSize: 24, // Smaller on mobile
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              if (isTabletOrLarger)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Property Listings',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _searchController,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Search listings...',
                    hintStyle: TextStyle(color: Colors.grey),
                    prefixIcon: Icon(Icons.search, color: Colors.grey),
                    filled: true,
                    fillColor: Colors.grey[900],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onChanged: (_) => _filterListings(),
                ),
              ),
              SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child:
                    isMobile
                        ? Column(
                          children: [
                            _buildFilterDropdown('All Types', [
                              'All Types',
                              'For Sale',
                              'For Rent',
                            ]),
                            SizedBox(height: 8),
                            _buildFilterDropdown('All Status', [
                              'All Status',
                              'Active',
                              'Banned',
                              'Rented',
                            ]),
                          ],
                        )
                        : Row(
                          children: [
                            Expanded(
                              child: _buildFilterDropdown('All Types', [
                                'All Types',
                                'For Sale',
                                'For Rent',
                              ]),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: _buildFilterDropdown('All Status', [
                                'All Status',
                                'Active',
                                'Banned',
                                'Rented',
                              ]),
                            ),
                          ],
                        ),
              ),
              SizedBox(height: 16),
              Expanded(
                child:
                    _isLoading
                        ? Center(child: CircularProgressIndicator())
                        : _buildResponsivePropertyTable(constraints),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterDropdown(String value, List<String> items) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          icon: Icon(Icons.keyboard_arrow_down, color: Colors.white),
          iconSize: 24,
          elevation: 16,
          style: TextStyle(color: Colors.white),
          dropdownColor: Colors.grey[850],
          isExpanded: true,
          onChanged: (String? newValue) {
            // Handle dropdown change
            if (newValue != null && newValue.contains('Status')) {
              if (newValue != 'All Status') {
                setState(() {
                  _selectedStatus = newValue.replaceAll(' Status', '');
                  _filterListings();
                });
              } else {
                setState(() {
                  _selectedStatus = 'All';
                  _filterListings();
                });
              }
            }
          },
          items:
              items.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildResponsivePropertyTable(BoxConstraints constraints) {
    if (_filteredListings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_work, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No properties found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[400],
              ),
            ),
          ],
        ),
      );
    }

    // For mobile view, simplify the table
    final isMobile = constraints.maxWidth <= 600;

    if (isMobile) {
      return _buildMobilePropertyList();
    } else {
      return _buildPropertyTable();
    }
  }

  Widget _buildMobilePropertyList() {
    return ListView.builder(
      itemCount: _filteredListings.length,
      itemBuilder: (context, index) {
        final property =
            _filteredListings[index].data() as Map<String, dynamic>;
        final propertyId = _filteredListings[index].id;

        final String title = property['title'] ?? 'Untitled';
        final String location = property['location'] ?? 'No location';
        final double price =
            property['price'] is double
                ? property['price']
                : property['price'] is int
                ? (property['price'] as int).toDouble()
                : 0.0;
        final String status = property['status'] ?? 'unavailable';
        final String propertyType = property['propertyType'] ?? 'Unknown';
        final List<dynamic> imageUrls = property['images'] ?? [];
        final String ownerName =
            property['userName'] ?? property['ownerName'] ?? 'Unknown Seller';
        final String dateListed =
            property['createdAt'] != null
                ? 'Jan 15, 2023' // Replace with actual date formatting
                : 'Jan 15, 2023';
        final String saleType =
            propertyType == 'Apartment' ? 'For Sale' : 'For Rent';

        return Card(
          margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          color: Colors.grey[900],
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey[800],
                        child:
                            imageUrls.isNotEmpty
                                ? _buildImageWidget(imageUrls[0])
                                : Icon(Icons.home, color: Colors.white),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            location,
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(26),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  saleType,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.withAlpha(51),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Active',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                Divider(color: Colors.grey[800]),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '₱${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Price',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ownerName,
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        Text(
                          'Seller',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          dateListed.split(', ')[0],
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        Text(
                          dateListed.split(', ')[1],
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                    IconButton(
                      icon: Icon(Icons.more_vert, color: Colors.white),
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: Colors.grey[850],
                          builder:
                              (context) => Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ListTile(
                                    leading: Icon(
                                      Icons.visibility,
                                      color: Colors.white,
                                    ),
                                    title: Text(
                                      'View Details',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    onTap: () {
                                      Navigator.pop(context);
                                      // View details action
                                    },
                                  ),
                                  ListTile(
                                    leading: Icon(
                                      Icons.edit,
                                      color: Colors.white,
                                    ),
                                    title: Text(
                                      'Edit Listing',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    onTap: () {
                                      Navigator.pop(context);
                                      // Edit listing action
                                    },
                                  ),
                                  ListTile(
                                    leading: Icon(
                                      Icons.block,
                                      color: Colors.white,
                                    ),
                                    title: Text(
                                      'Ban Listing',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    onTap: () {
                                      Navigator.pop(context);
                                      _togglePropertyStatus(propertyId, status);
                                    },
                                  ),
                                  ListTile(
                                    leading: Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    title: Text(
                                      'Delete Listing',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                    onTap: () {
                                      Navigator.pop(context);
                                      _showDeleteConfirmationDialog(
                                        propertyId,
                                        title,
                                      );
                                    },
                                  ),
                                ],
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPropertyTable() {
    if (_filteredListings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_work, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No properties found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[400],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.black,
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade800, width: 2),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: Text(
                    'Property',
                    style: TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Type',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Price',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Owner',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Date',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Status',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Actions',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _filteredListings.length,
            itemBuilder: (context, index) {
              final property =
                  _filteredListings[index].data() as Map<String, dynamic>;
              final propertyId = _filteredListings[index].id;

              final String title = property['title'] ?? 'Untitled';
              final String location = property['location'] ?? 'No location';
              final double price =
                  property['price'] is double
                      ? property['price']
                      : property['price'] is int
                      ? (property['price'] as int).toDouble()
                      : 0.0;
              final String status = property['status'] ?? 'unavailable';
              final String propertyType = property['propertyType'] ?? 'Unknown';
              final List<dynamic> imageUrls = property['images'] ?? [];
              final String ownerName =
                  property['userName'] ??
                  property['ownerName'] ??
                  'Unknown Seller';
              final String dateListed =
                  property['createdAt'] != null
                      ? 'Jan 15, 2023' // Replace with actual date formatting
                      : 'Jan 15, 2023';
              final String saleType =
                  propertyType == 'Apartment' ? 'For Sale' : 'For Rent';

              return Container(
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                height: 110, // Slightly taller for better spacing
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade900),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                width: 80,
                                height: 80,
                                color: Colors.grey[800],
                                child:
                                    imageUrls.isNotEmpty
                                        ? _buildImageWidget(imageUrls[0])
                                        : Icon(Icons.home, color: Colors.white),
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    title,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(height: 6),
                                  Text(
                                    location,
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 12,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(26),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          margin: EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            saleType,
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 12, color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: Text(
                          '₱${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              ownerName,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Seller',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              dateListed.split(
                                ', ',
                              )[0], // First part (month + day)
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              dateListed.split(', ')[1], // Second part (year)
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(51),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          margin: EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            'Active',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Center(
                        child: IconButton(
                          icon: Icon(Icons.more_horiz, color: Colors.white),
                          onPressed: () {
                            showMenu(
                              context: context,
                              position: RelativeRect.fromLTRB(
                                MediaQuery.of(context).size.width,
                                0,
                                0,
                                0,
                              ),
                              items: [
                                PopupMenuItem(
                                  value: 'view',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.visibility,
                                        color: Colors.white,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'View Details',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit, color: Colors.white),
                                      SizedBox(width: 8),
                                      Text(
                                        'Edit Listing',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'ban',
                                  child: Row(
                                    children: [
                                      Icon(Icons.block, color: Colors.white),
                                      SizedBox(width: 8),
                                      Text(
                                        'Ban Listing',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text(
                                        'Delete Listing',
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              color: Colors.grey[850],
                            ).then((value) {
                              if (value == 'view') {
                                // View details
                              } else if (value == 'edit') {
                                // Edit listing
                              } else if (value == 'ban') {
                                // Ban listing
                                _togglePropertyStatus(propertyId, status);
                              } else if (value == 'delete') {
                                _showDeleteConfirmationDialog(
                                  propertyId,
                                  title,
                                );
                              }
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
