import 'package:flutter/material.dart';
import 'dart:convert';
import '../firebase_auth.dart';
import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/firestore_collections.dart';
import 'property_detail_screen.dart';
import 'all_properties_screen.dart';

class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  int _selectedIndex = 0;
  String _selectedPropertyType = 'House';
  final List<String> _propertyTypes = ['House', 'Apartment'];

  // Add auth and user data variables
  final Auth _auth = Auth();
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  final Logger _logger = Logger();

  // Properties data
  List<Map<String, dynamic>> _featuredProperties = [];
  List<Map<String, dynamic>> _nearbyProperties = [];
  bool _loadingProperties = true;

  // Search controller
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadApprovedProperties();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_searchController.text.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      _loadApprovedProperties(); // Reset to show all properties
    } else if (_searchController.text.length >= 2) {
      // Only search with 2+ characters
      _performSearch(_searchController.text);
    }
  }

  // Load user data from Firestore
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userData = await _auth.getUserData();
      setState(() {
        _userData = userData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _logger.e('Error loading user data: $e');
    }
  }

  // Load approved properties from Firestore
  Future<void> _loadApprovedProperties() async {
    setState(() {
      _loadingProperties = true;
    });

    try {
      // Query properties that are verified/approved
      QuerySnapshot snapshot =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('isVerified', isEqualTo: true)
              .where('verificationStatus', isEqualTo: 'approved')
              .orderBy('createdAt', descending: true)
              .get();

      final List<Map<String, dynamic>> allProperties =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return {...data, 'id': doc.id};
          }).toList();

      // Filter properties based on selected property type and listing type
      final List<Map<String, dynamic>> filteredProperties =
          allProperties.where((property) {
            if (_selectedPropertyType == 'House') {
              return property['propertyType'] == 'House' &&
                  property['listingType'] == 'sale';
            } else if (_selectedPropertyType == 'Apartment') {
              return property['propertyType'] == 'Apartment' &&
                  property['listingType'] == 'rent';
            }
            return true;
          }).toList();

      // Split properties into featured and nearby
      setState(() {
        if (filteredProperties.isNotEmpty) {
          _featuredProperties = filteredProperties.take(5).toList();
          _nearbyProperties = filteredProperties.skip(5).take(5).toList();
        } else {
          _featuredProperties = [];
          _nearbyProperties = [];
        }
        _loadingProperties = false;
      });
    } catch (e) {
      _logger.e('Error loading properties: $e');
      setState(() {
        _loadingProperties = false;
      });
    }
  }

  Future<void> _performSearch(String query) async {
    setState(() {
      _isSearching = true;
    });

    try {
      // First search by title
      QuerySnapshot titleSnapshot =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('isVerified', isEqualTo: true)
              .where('verificationStatus', isEqualTo: 'approved')
              .orderBy('title')
              .startAt([query])
              .endAt(['$query\uf8ff'])
              .get();

      // Then search by location
      QuerySnapshot locationSnapshot =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('isVerified', isEqualTo: true)
              .where('verificationStatus', isEqualTo: 'approved')
              .orderBy('location')
              .startAt([query])
              .endAt(['$query\uf8ff'])
              .get();

      // Combine results and remove duplicates
      Set<String> uniqueIds = {};
      List<Map<String, dynamic>> combinedResults = [];

      for (var doc in titleSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final id = doc.id;
        if (!uniqueIds.contains(id)) {
          uniqueIds.add(id);
          combinedResults.add({...data, 'id': id});
        }
      }

      for (var doc in locationSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final id = doc.id;
        if (!uniqueIds.contains(id)) {
          uniqueIds.add(id);
          combinedResults.add({...data, 'id': id});
        }
      }

      setState(() {
        _searchResults = combinedResults;

        // Update featured and nearby properties with search results
        if (_searchResults.isNotEmpty) {
          _featuredProperties = _searchResults;
          _nearbyProperties =
              []; // Clear nearby properties when showing search results
        }

        _isSearching = false;
      });
    } catch (e) {
      _logger.e('Error searching properties: $e');
      setState(() {
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get user's name and address from userData
    final String firstName =
        _userData != null ? _userData!['firstName'] ?? '' : '';
    final String lastName =
        _userData != null ? _userData!['lastName'] ?? '' : '';
    final String displayName = '$firstName $lastName'.trim();
    final String userAddress =
        _userData != null
            ? _userData!['address'] ?? 'No address'
            : 'Loading...';
    final String? profileImage =
        _userData != null ? _userData!['profileImage'] : null;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section with user info and menu
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        // User profile image
                        _isLoading
                            ? Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.grey,
                              ),
                              child: const Center(
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            )
                            : Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.grey[200],
                                image:
                                    profileImage != null &&
                                            profileImage.isNotEmpty
                                        ? _getProfileImageProvider(profileImage)
                                        : null,
                              ),
                              child:
                                  profileImage == null || profileImage.isEmpty
                                      ? const Center(
                                        child: Icon(
                                          Icons.person,
                                          color: Colors.grey,
                                        ),
                                      )
                                      : null,
                            ),
                        const SizedBox(width: 12),
                        // User greeting and location
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Hi, ${displayName.isEmpty ? 'User' : displayName}!',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  userAddress,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue[600],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_drop_down,
                                  color: Colors.blue[600],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Search bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.search, color: Colors.grey),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextField(
                                controller: _searchController,
                                decoration: InputDecoration(
                                  hintText:
                                      'Search property name or location...',
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                  border: InputBorder.none,
                                ),
                                onSubmitted: (value) {
                                  if (value.isNotEmpty) {
                                    _performSearch(value);
                                  }
                                },
                              ),
                            ),
                            if (_searchController.text.isNotEmpty)
                              GestureDetector(
                                onTap: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchResults = [];
                                    _isSearching = false;
                                  });
                                  _loadApprovedProperties(); // Reset to show all properties
                                },
                                child: const Icon(
                                  Icons.clear,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Property type filters
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _propertyTypes.length,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemBuilder: (context, index) {
                    final isSelected =
                        _propertyTypes[index] == _selectedPropertyType;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedPropertyType = _propertyTypes[index];
                          // Reload properties when property type changes
                          _loadApprovedProperties();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 20),
                        child: Column(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? Colors.blue[200]
                                        : Colors.grey[200],
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Icon(
                                  _getPropertyIcon(_propertyTypes[index]),
                                  color:
                                      isSelected ? Colors.grey : Colors.black54,
                                  size: 30,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _propertyTypes[index],
                              style: TextStyle(
                                color:
                                    isSelected
                                        ? Colors.lightBlue
                                        : Colors.black54,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 20),

              // Featured properties
              Column(
                children: [
                  _buildSectionHeader('Featured Properties'),
                  if (_isSearching)
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  if (_searchController.text.isNotEmpty && !_isSearching)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 8.0,
                      ),
                      child: Text(
                        'Search results for "${_searchController.text}"',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  _loadingProperties
                      ? const Center(child: CircularProgressIndicator())
                      : _featuredProperties.isEmpty
                      ? (_searchController.text.isNotEmpty
                          ? Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.search_off,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  'No properties found for "${_searchController.text}"',
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                              ],
                            ),
                          )
                          : const Center(
                            child: Text('No featured properties found'),
                          ))
                      : SizedBox(
                        height:
                            MediaQuery.of(context).size.width > 600
                                ? 280.0 *
                                    (_featuredProperties.length / 3)
                                        .ceil()
                                        .toDouble()
                                : 240.0 *
                                    (_featuredProperties.length / 2)
                                        .ceil()
                                        .toDouble(),
                        child: GridView.builder(
                          physics:
                              const NeverScrollableScrollPhysics(), // Important to prevent scrolling
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount:
                                    MediaQuery.of(context).size.width > 600
                                        ? 3
                                        : 2,
                                mainAxisExtent:
                                    MediaQuery.of(context).size.width > 600
                                        ? 280
                                        : 240, // Fixed heights
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                              ),
                          itemCount: _featuredProperties.length,
                          itemBuilder: (context, index) {
                            final property = _featuredProperties[index];
                            final String title =
                                property['title'] ?? 'No Title';
                            final String location =
                                property['location'] ?? 'No Location';
                            final double price =
                                (property['price'] is double)
                                    ? property['price']
                                    : (property['price'] is int)
                                    ? (property['price'] as int).toDouble()
                                    : 0.0;
                            final String priceText =
                                '₱${price.toStringAsFixed(2)}';
                            final String period =
                                property['listingType'] == 'rent'
                                    ? '/ month'
                                    : '';
                            final List<dynamic> images =
                                property['images'] ?? [];

                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => PropertyDetailScreen(
                                          property: property,
                                        ),
                                  ),
                                );
                              },
                              child: _buildVerticalPropertyCard(
                                name: title,
                                location: location,
                                price: priceText,
                                period: period,
                                imageUrl: images.isNotEmpty ? images[0] : null,
                                listingType: property['listingType'] ?? 'sale',
                              ),
                            );
                          },
                        ),
                      ),
                ],
              ),

              const SizedBox(height: 20),

              // Nearby properties
              Column(
                children: [
                  _buildSectionHeader('Nearby Properties'),
                  _loadingProperties
                      ? const Center(child: CircularProgressIndicator())
                      : _nearbyProperties.isEmpty
                      ? const Center(child: Text('No nearby properties found'))
                      : SizedBox(
                        height:
                            MediaQuery.of(context).size.width > 600
                                ? 280.0 *
                                    (_nearbyProperties.length / 3)
                                        .ceil()
                                        .toDouble() // For tablets (3 columns)
                                : 240.0 *
                                    (_nearbyProperties.length / 2)
                                        .ceil()
                                        .toDouble(), // For phones (2 columns)
                        child: GridView.builder(
                          physics:
                              const NeverScrollableScrollPhysics(), // Important to prevent scrolling
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount:
                                    MediaQuery.of(context).size.width > 600
                                        ? 3
                                        : 2,
                                mainAxisExtent:
                                    MediaQuery.of(context).size.width > 600
                                        ? 280
                                        : 240, // Fixed heights
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                              ),
                          itemCount: _nearbyProperties.length,
                          itemBuilder: (context, index) {
                            final property = _nearbyProperties[index];
                            final String title =
                                property['title'] ?? 'No Title';
                            final String location =
                                property['location'] ?? 'No Location';
                            final double price =
                                (property['price'] is double)
                                    ? property['price']
                                    : (property['price'] is int)
                                    ? (property['price'] as int).toDouble()
                                    : 0.0;
                            final String priceText =
                                '₱${price.toStringAsFixed(2)}';
                            final String period =
                                property['listingType'] == 'rent'
                                    ? '/ month'
                                    : '';
                            final List<dynamic> images =
                                property['images'] ?? [];

                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => PropertyDetailScreen(
                                          property: property,
                                        ),
                                  ),
                                );
                              },
                              child: _buildVerticalPropertyCard(
                                name: title,
                                location: location,
                                price: priceText,
                                period: period,
                                imageUrl: images.isNotEmpty ? images[0] : null,
                                listingType: property['listingType'] ?? 'sale',
                              ),
                            );
                          },
                        ),
                      ),
                ],
              ),

              const SizedBox(height: 20),

              // Map View Card
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.map, color: Colors.blue, size: 28),
                            const SizedBox(width: 12),
                            const Text(
                              'Map View',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              icon: const Icon(
                                Icons.arrow_forward,
                                color: Colors.blue,
                              ),
                              onPressed: () {
                                Navigator.pushNamed(context, '/map');
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'View all properties on an interactive map to find the perfect location for your next home.',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite_border),
            label: 'Favourite',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            label: 'Messages',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  void _onItemTapped(int index) {
    if (index == _selectedIndex) return;

    switch (index) {
      case 0:
        // Already on home
        setState(() {
          _selectedIndex = index;
        });
        break;
      case 1:
        // Map
        Navigator.pushNamed(context, '/favorite');
        break;
      case 2:
        // Messages
        Navigator.pushNamed(context, '/messages');
        break;
      case 3:
        // Profile
        Navigator.pushNamed(context, '/profile');
        break;
    }
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => AllPropertiesScreen(
                        title: title,
                        isFeatured: title == 'Featured Properties',
                      ),
                ),
              );
            },
            child: Text(
              'View All',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerticalPropertyCard({
    required String name,
    required String location,
    required String price,
    required String period,
    String? imageUrl,
    String listingType = 'sale',
  }) {
    final double cardHeight = 220; // ADJUST THIS VALUE as needed
    final double imageHeight = 120; // ADJUST THIS VALUE as needed

    return SizedBox(
      height: cardHeight,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Fixed height image container
            SizedBox(
              height: imageHeight,
              width: double.infinity,
              child: Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      image:
                          imageUrl != null
                              ? DecorationImage(
                                image: NetworkImage(imageUrl),
                                fit: BoxFit.cover,
                              )
                              : null,
                    ),
                    child:
                        imageUrl == null
                            ? const Center(
                              child: Icon(
                                Icons.image,
                                size: 40,
                                color: Colors.white,
                              ),
                            )
                            : null,
                  ),
                  // For Sale/Rent tag
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            listingType == 'rent'
                                ? Colors.green.withValues(
                                  red: 76,
                                  green: 175,
                                  blue: 80,
                                  alpha: 230,
                                )
                                : Colors.blue.withValues(
                                  red: 33,
                                  green: 150,
                                  blue: 243,
                                  alpha: 230,
                                ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        listingType == 'rent' ? 'For Rent' : 'For Sale',
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Fixed remaining space for details
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 10,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 2),
                        Expanded(
                          child: Text(
                            location,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          price,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          period,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPropertyIcon(String type) {
    switch (type) {
      case 'House':
        return Icons.home;
      case 'Apartment':
        return Icons.apartment;
      default:
        return Icons.home;
    }
  }

  DecorationImage? _getProfileImageProvider(String imageData) {
    // Check if it's a URL (starts with http or https)
    if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
      return DecorationImage(image: NetworkImage(imageData), fit: BoxFit.cover);
    }

    // Try to decode as base64
    try {
      return DecorationImage(
        image: MemoryImage(base64Decode(imageData)),
        fit: BoxFit.cover,
      );
    } catch (e) {
      // If base64 decoding fails, return null
      return null;
    }
  }
}
