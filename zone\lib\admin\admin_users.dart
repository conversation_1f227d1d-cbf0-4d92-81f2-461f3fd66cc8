import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/web.dart';
import '../models/firestore_collections.dart';
import 'dart:convert';

class AdminUsers extends StatefulWidget {
  const AdminUsers({super.key});

  @override
  State<AdminUsers> createState() => _AdminUsersState();
}

class _AdminUsersState extends State<AdminUsers>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  bool _isLoading = false;
  List<DocumentSnapshot> _users = [];
  List<DocumentSnapshot> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      _filterUsersByType(_tabController.index);
    }
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final QuerySnapshot snapshot =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .orderBy('createdAt', descending: true)
              .get();

      if (mounted) {
        setState(() {
          _users = snapshot.docs;
          _filterUsersByType(_tabController.index);
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger().e('Error loading users: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterUsersByType(int tabIndex) {
    String role;
    switch (tabIndex) {
      case 0:
        role = 'Seller';
        break;
      case 1:
        role = 'Landlord';
        break;
      case 2:
        role = 'Buyer';
        break;
      case 3:
        role = 'Tenant';
        break;
      default:
        role = 'Seller';
    }

    setState(() {
      _filteredUsers =
          _users.where((doc) {
            final data = doc.data() as Map<String, dynamic>;
            final userRole = data['role'] as String? ?? '';

            if (_searchController.text.isEmpty) {
              return userRole == role;
            } else {
              final String searchQuery = _searchController.text.toLowerCase();
              final String firstName =
                  (data['firstName'] as String? ?? '').toLowerCase();
              final String lastName =
                  (data['lastName'] as String? ?? '').toLowerCase();
              final String email =
                  (data['email'] as String? ?? '').toLowerCase();

              return userRole == role &&
                  (firstName.contains(searchQuery) ||
                      lastName.contains(searchQuery) ||
                      email.contains(searchQuery));
            }
          }).toList();
    });
  }

  void _filterUsers(String query) {
    _filterUsersByType(_tabController.index);
  }

  Future<void> _toggleUserStatus(String userId, bool isBanned) async {
    try {
      await FirebaseFirestore.instance
          .collection(FirestoreCollections.users)
          .doc(userId)
          .update({'status': isBanned ? 'active' : 'banned'});

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('User status updated')));
        _loadUsers();
      }
    } catch (e) {
      Logger().e('Error updating user status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user status: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'User Accounts',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          CircleAvatar(
            backgroundColor: Colors.white,
            radius: 16,
            child: Icon(Icons.person, color: Colors.grey),
          ),
          SizedBox(width: 16),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade800),
              borderRadius: BorderRadius.circular(4),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey,
              indicator: BoxDecoration(
                color: Colors.black,
                border: Border(
                  bottom: BorderSide(color: Colors.blue, width: 2),
                ),
              ),
              tabs: [
                Tab(text: 'Sellers'),
                Tab(text: 'Landlords'),
                Tab(text: 'Buyers'),
                Tab(text: 'Tenants'),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search users...',
                hintStyle: TextStyle(color: Colors.grey),
                prefixIcon: Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Colors.grey[900],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
              ),
              onChanged: _filterUsers,
            ),
          ),
          SizedBox(height: 16),
          Expanded(
            child:
                _isLoading
                    ? Center(child: CircularProgressIndicator())
                    : _buildUsersTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTable() {
    if (_filteredUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_off, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No users found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[400],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.black,
            border: Border(bottom: BorderSide(color: Colors.grey.shade800)),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 4,
                child: Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: Text(
                    'User',
                    style: TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'Email',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Properties',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'Joined',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'Status',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'Actions',
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _filteredUsers.length,
            itemBuilder: (context, index) {
              final user = _filteredUsers[index].data() as Map<String, dynamic>;
              final userId = _filteredUsers[index].id;

              final String firstName = user['firstName'] ?? '';
              final String lastName = user['lastName'] ?? '';
              final String email = user['email'] ?? '';
              final String profileImage = user['profileImage'] ?? '';
              final int propertyCount = user['propertyCount'] ?? 0;
              final bool isBanned = user['status'] == 'banned';

              // Format the timestamp for display
              final Timestamp? createdAt = user['createdAt'] as Timestamp?;
              String joinedDate = 'Unknown';
              if (createdAt != null) {
                final DateTime date = createdAt.toDate();
                joinedDate =
                    '${_getMonthName(date.month)} ${date.day}, ${date.year}';
              }

              return Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade900),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 4,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(24),
                              child: Container(
                                width: 36,
                                height: 36,
                                color: Colors.grey[800],
                                child:
                                    profileImage.isNotEmpty
                                        ? _buildProfileImage(profileImage)
                                        : Icon(
                                          Icons.person,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '$firstName $lastName',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Text(
                        email,
                        style: TextStyle(color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        propertyCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        joinedDate,
                        style: TextStyle(color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isBanned
                                  ? Colors.red.withValues(
                                    red: 200,
                                    green: 0,
                                    blue: 0,
                                    alpha: 0.2,
                                  )
                                  : Colors.green.withValues(
                                    red: 0,
                                    green: 150,
                                    blue: 0,
                                    alpha: 0.2,
                                  ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          isBanned ? 'Banned' : 'Active',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isBanned ? Colors.red : Colors.green,
                            fontWeight: FontWeight.bold,
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: PopupMenuButton<String>(
                        icon: Icon(Icons.more_vert, color: Colors.white),
                        color: Colors.grey[900],
                        itemBuilder:
                            (context) => [
                              PopupMenuItem(
                                value: 'ban',
                                child: Row(
                                  children: [
                                    Icon(
                                      isBanned
                                          ? Icons.check_circle
                                          : Icons.block,
                                      color:
                                          isBanned ? Colors.green : Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      isBanned ? 'Unban User' : 'Ban User',
                                      style: TextStyle(
                                        color:
                                            isBanned
                                                ? Colors.green
                                                : Colors.red,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                        onSelected: (value) {
                          if (value == 'ban') {
                            _toggleUserStatus(userId, isBanned);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImage(String imageData) {
    // Check if it's a URL (starts with http or https)
    if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
      return Image.network(
        imageData,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Icon(Icons.person, color: Colors.white, size: 20);
        },
      );
    }

    // Try to decode as base64
    try {
      return Image.memory(
        base64Decode(imageData),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Icon(Icons.person, color: Colors.white, size: 20);
        },
      );
    } catch (e) {
      return Icon(Icons.person, color: Colors.white, size: 20);
    }
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'Jan';
      case 2:
        return 'Feb';
      case 3:
        return 'Mar';
      case 4:
        return 'Apr';
      case 5:
        return 'May';
      case 6:
        return 'Jun';
      case 7:
        return 'Jul';
      case 8:
        return 'Aug';
      case 9:
        return 'Sep';
      case 10:
        return 'Oct';
      case 11:
        return 'Nov';
      case 12:
        return 'Dec';
      default:
        return '';
    }
  }
}
