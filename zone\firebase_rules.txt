rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false; // Default deny all
    }

    // User profiles - users can read all profiles but only edit their own
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Properties - users can read all properties but only modify their own
    match /properties/{propertyId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
                            resource.data.ownerId == request.auth.uid;
    }

    // Conversations - users can only access conversations they are part of
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
                          request.auth.uid in resource.data.participants;

      // Messages in conversations - users can only access if they're part of the parent conversation
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
                            request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      }
    }

    // Allow creation of new conversations
    match /conversations/{conversationId} {
      allow create: if request.auth != null && 
                      request.auth.uid in request.resource.data.participants;
    }
    
    // Favorites - users can read and write their own favorites
    match /favorites/{favoriteId} {
      allow read: if request.auth != null && (
        // Allow reading individual documents owned by the user
        request.auth.uid == resource.data.userId ||
        // Allow querying the collection by userId
        request.query.limit <= 100 && request.query.filters.userId == request.auth.uid
      );
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Views - users can read and write their own view records
    match /views/{viewId} {
      allow read: if request.auth != null && (
        // Allow reading individual documents owned by the user
        request.auth.uid == resource.data.userId ||
        // Allow querying the collection by userId
        request.query.limit <= 100 && request.query.filters.userId == request.auth.uid
      );
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
} 