import 'package:flutter/material.dart';
import '../widgets/reusable_widget.dart';
import 'dart:math';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  @override
  Widget build(BuildContext context) {
    // Get screen size for responsiveness
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: const AssetImage('assets/unsplash.jpg'),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.black.withValues(red: 0, green: 0, blue: 0, alpha: 0.5),
              BlendMode.darken,
            ),
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: EdgeInsets.all(size.width * 0.06), // Responsive padding
            child: Column(
              children: [
                const Spacer(),
                // Title text with responsive font size
                Text(
                  'Discover and Find Your',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 24 : 28, // Responsive font size
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'perfect Dream House',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 24 : 28, // Responsive font size
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: size.height * 0.02), // Responsive spacing
                // Subtitle text with responsive font size
                Text(
                  'Browse through all categories and discover the most suitable place for you to stay',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 14 : 16, // Responsive font size
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                const Spacer(),
                // Register button using custom reusable widget
                Center(
                  child: CustomButton(
                    text: 'Register',
                    onPressed: () {
                      Navigator.pushNamed(context, '/register');
                    },
                    // Standard button size common in apps
                    width: min(size.width * 0.85, 400),
                    height: 56,
                    borderRadius: 12.0,
                  ),
                ),
                SizedBox(height: size.height * 0.02), // Responsive spacing
                // Login text with responsive font size
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'If you have account? ',
                      style: TextStyle(
                        fontSize:
                            isSmallScreen ? 14 : 16, // Responsive font size
                        color: Colors.white,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pushNamed(context, '/login');
                      },
                      child: Text(
                        'Login',
                        style: TextStyle(
                          fontSize:
                              isSmallScreen ? 14 : 16, // Responsive font size
                          color: const Color(0xFF0288D1),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.02), // Responsive spacing
              ],
            ),
          ),
        ),
      ),
    );
  }
}
