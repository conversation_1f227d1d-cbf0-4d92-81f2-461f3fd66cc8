// Firestore Collections and Schema Guide

/*
This file serves as a reference for all Firestore collections and their structure.
It ensures consistency across the application when accessing Firestore data.
*/

// Collection Names
class FirestoreCollections {
  static const String users = 'users';
  static const String properties = 'properties';
  static const String saved = 'saved';
  static const String messages = 'messages';
}

// User Document Structure
/*
{
  "uid": "string", // User ID from Firebase Auth
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "address": "string",
  "contact": "string",
  "username": "string",
  "role": "string", // "Tenant", "Landlord", "Buyer", "Seller"
  "profileImage": "string", // Base64 encoded image
  "createdAt": "timestamp"
}
*/

// Property Document Structure
/*
{
  "propertyId": "string", // Document ID
  "ownerId": "string", // Owner's User ID
  "title": "string",
  "description": "string",
  "address": "string",
  "price": "number",
  "propertyType": "string", // "House", "Apartment", etc.
  "bedrooms": "number",
  "bathrooms": "number",
  "area": "number", // Square footage
  "amenities": ["string"], // Array of amenities
  "images": ["string"], // Array of image URLs or Base64
  "status": "string", // "Available", "Sold", "Rented"
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
*/

// Saved Property Document Structure
/*
{
  "userId": "string", // User who saved the property
  "propertyId": "string", // The saved property ID
  "savedAt": "timestamp"
}
*/

// Message Document Structure
/*
{
  "messageId": "string", // Document ID
  "senderId": "string", // Sender's User ID
  "recipientId": "string", // Recipient's User ID
  "content": "string", // Message content
  "propertyId": "string", // Optional, if the message is about a property
  "read": "boolean", // Whether the message has been read
  "createdAt": "timestamp"
}
*/
