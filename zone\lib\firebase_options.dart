// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCO0FsF_USySrv554KaKUrEWHe-V16FXuw',
    appId: '1:1079911448359:web:06cc06ff608ffc5388b302',
    messagingSenderId: '1079911448359',
    projectId: 'edge-property',
    authDomain: 'edge-property.firebaseapp.com',
    storageBucket: 'edge-property.firebasestorage.app',
    measurementId: 'G-PPBHTWWPR2',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC9E3-cvqZ66B1QIyEg4ESgbuctvwf60HU',
    appId: '1:1079911448359:android:336609c43610e35a88b302',
    messagingSenderId: '1079911448359',
    projectId: 'edge-property',
    storageBucket: 'edge-property.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAibTxhM2_-zRxQVWKwtkhl4-ZjN8WwhjM',
    appId: '1:1079911448359:ios:039bf118bcca223f88b302',
    messagingSenderId: '1079911448359',
    projectId: 'edge-property',
    storageBucket: 'edge-property.firebasestorage.app',
    iosBundleId: 'com.example.zone',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAibTxhM2_-zRxQVWKwtkhl4-ZjN8WwhjM',
    appId: '1:1079911448359:ios:039bf118bcca223f88b302',
    messagingSenderId: '1079911448359',
    projectId: 'edge-property',
    storageBucket: 'edge-property.firebasestorage.app',
    iosBundleId: 'com.example.zone',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCO0FsF_USySrv554KaKUrEWHe-V16FXuw',
    appId: '1:1079911448359:web:fdf7ef823c09a7e688b302',
    messagingSenderId: '1079911448359',
    projectId: 'edge-property',
    authDomain: 'edge-property.firebaseapp.com',
    storageBucket: 'edge-property.firebasestorage.app',
    measurementId: 'G-CN0D03ZH05',
  );

}