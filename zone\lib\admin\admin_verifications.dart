import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/firestore_collections.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:logger/logger.dart';
import 'package:intl/intl.dart';

// String extension for capitalizing first letter
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

class AdminVerifications extends StatefulWidget {
  const AdminVerifications({super.key});

  @override
  State<AdminVerifications> createState() => _AdminVerificationsState();
}

class _AdminVerificationsState extends State<AdminVerifications>
    with SingleTickerProviderStateMixin {
  final Logger _logger = Logger();
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Mark notifications as read when this screen opens
    _markNotificationsAsRead();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _markNotificationsAsRead() async {
    try {
      final batch = FirebaseFirestore.instance.batch();
      final notifications =
          await FirebaseFirestore.instance
              .collection('notifications')
              .where('type', isEqualTo: 'verification_request')
              .where('read', isEqualTo: false)
              .get();

      for (var doc in notifications.docs) {
        batch.update(doc.reference, {'read': true});
      }

      await batch.commit();
    } catch (e) {
      _logger.e('Error marking notifications as read: $e');
    }
  }

  Future<void> _updateVerificationStatus(
    String propertyId,
    bool isApproved,
    String feedback,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Begin a batch write
      final batch = FirebaseFirestore.instance.batch();

      // Update the property document
      final propertyRef = FirebaseFirestore.instance
          .collection(FirestoreCollections.properties)
          .doc(propertyId);

      batch.update(propertyRef, {
        'isVerified': isApproved,
        'verificationStatus': isApproved ? 'approved' : 'declined',
        'verificationDate': DateTime.now().millisecondsSinceEpoch,
        'adminFeedback': feedback,
      });

      // Update the notification
      final notificationQuery =
          await FirebaseFirestore.instance
              .collection('notifications')
              .where('propertyId', isEqualTo: propertyId)
              .where('type', isEqualTo: 'verification_request')
              .get();

      for (var doc in notificationQuery.docs) {
        batch.update(doc.reference, {
          'status': isApproved ? 'approved' : 'declined',
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // Create a notification for the seller
      final propertyDoc = await propertyRef.get();
      final sellerId = propertyDoc.data()?['userId'];

      if (sellerId != null) {
        final notificationRef =
            FirebaseFirestore.instance.collection('user_notifications').doc();
        batch.set(notificationRef, {
          'userId': sellerId,
          'title': 'Listing ${isApproved ? 'Approved' : 'Declined'}',
          'message':
              isApproved
                  ? 'Your listing "${propertyDoc.data()?['title']}" has been approved and is now visible to users.'
                  : 'Your listing "${propertyDoc.data()?['title']}" has been declined. Reason: $feedback',
          'read': false,
          'type': 'listing_verification',
          'propertyId': propertyId,
          'timestamp': FieldValue.serverTimestamp(),
        });
      }

      // Commit the batch
      await batch.commit();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Listing ${isApproved ? 'approved' : 'declined'} successfully',
          ),
          backgroundColor: isApproved ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      _logger.e('Error updating verification status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showFeedbackDialog(String propertyId, bool isApproved) {
    final TextEditingController feedbackController = TextEditingController(
      text:
          isApproved
              ? 'Your listing has been approved.'
              : 'Your listing does not meet our requirements. Please review our guidelines and try again.',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isApproved ? 'Approve Listing' : 'Decline Listing'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isApproved
                      ? 'Provide any additional information to the seller:'
                      : 'Please provide a reason for declining this listing:',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: feedbackController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    hintText: 'Enter feedback...',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _updateVerificationStatus(
                    propertyId,
                    isApproved,
                    feedbackController.text.trim(),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isApproved ? Colors.green : Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: Text(isApproved ? 'Approve' : 'Decline'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verification Requests'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Pending'),
            Tab(text: 'Approved'),
            Tab(text: 'Declined'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildPropertyList('pending'),
                  _buildPropertyList('approved'),
                  _buildPropertyList('declined'),
                ],
              ),
    );
  }

  Widget _buildPropertyList(String status) {
    return StreamBuilder<QuerySnapshot>(
      stream:
          FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('verificationStatus', isEqualTo: status)
              .orderBy('createdAt', descending: true)
              .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error: ${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        final properties = snapshot.data?.docs ?? [];

        if (properties.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  status == 'pending'
                      ? Icons.hourglass_empty
                      : status == 'approved'
                      ? Icons.check_circle_outline
                      : Icons.cancel_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${status.capitalize()} Listings',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  status == 'pending'
                      ? 'When new listings are created, they will appear here for review.'
                      : status == 'approved'
                      ? 'Listings you approve will be shown here.'
                      : 'Listings you decline will be shown here.',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: properties.length,
          itemBuilder: (context, index) {
            final property = properties[index];
            final data = property.data() as Map<String, dynamic>;
            final List<dynamic> images = data['images'] ?? [];
            final String title = data['title'] ?? 'No Title';
            final String location = data['location'] ?? 'No Location';
            final double price =
                data['price'] is double
                    ? data['price']
                    : data['price'] is int
                    ? (data['price'] as int).toDouble()
                    : 0.0;
            final String sellerName = data['userName'] ?? 'Unknown';
            final int createdAt = data['createdAt'] ?? 0;
            final DateTime createdDate = DateTime.fromMillisecondsSinceEpoch(
              createdAt,
            );
            final String formattedDate = DateFormat(
              'MMM d, yyyy',
            ).format(createdDate);

            return Card(
              margin: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Property image
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(4),
                    ),
                    child: SizedBox(
                      height: 200,
                      width: double.infinity,
                      child:
                          images.isNotEmpty
                              ? CachedNetworkImage(
                                imageUrl: images[0],
                                fit: BoxFit.cover,
                                placeholder:
                                    (context, url) => const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                errorWidget:
                                    (context, url, error) =>
                                        const Center(child: Icon(Icons.error)),
                              )
                              : Container(
                                color: Colors.grey[300],
                                child: const Icon(
                                  Icons.image,
                                  size: 64,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ),

                  // Property details
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                location,
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Price: ₱${price.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            const Icon(Icons.person, size: 16),
                            const SizedBox(width: 4),
                            Text('Seller: $sellerName'),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 4),
                            Text('Created: $formattedDate'),
                          ],
                        ),

                        if (status == 'pending') ...[
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  icon: const Icon(Icons.visibility),
                                  label: const Text('View Details'),
                                  onPressed: () {
                                    // Navigate to detail view
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.check),
                                  label: const Text('Approve'),
                                  onPressed:
                                      () => _showFeedbackDialog(
                                        property.id,
                                        true,
                                      ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.clear),
                                  label: const Text('Decline'),
                                  onPressed:
                                      () => _showFeedbackDialog(
                                        property.id,
                                        false,
                                      ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ] else ...[
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 12,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  status == 'approved'
                                      ? Colors.green.withValues(
                                        red: 0,
                                        green: 128,
                                        blue: 0,
                                        alpha: 25,
                                      )
                                      : Colors.orange.withValues(
                                        red: 255,
                                        green: 165,
                                        blue: 0,
                                        alpha: 25,
                                      ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  status == 'approved'
                                      ? 'Approved'
                                      : 'Declined',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        status == 'approved'
                                            ? Colors.green
                                            : Colors.orange,
                                  ),
                                ),
                                if (data['adminFeedback'] != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    data['adminFeedback'],
                                    style: TextStyle(color: Colors.grey[800]),
                                  ),
                                ],
                                if (data['verificationDate'] != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    'Date: ${DateFormat('MMM d, yyyy').format(DateTime.fromMillisecondsSinceEpoch(data['verificationDate']))}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
