import '../models/faq_data.dart';

class FAQAssistantService {
  Map<String, List<String>> keywords = PropertyFAQ.getKeywords();

  String getResponse(String question, Map<String, dynamic> property) {
    question = question.toLowerCase();

    // Check if question contains any keywords
    for (var category in keywords.keys) {
      for (var keyword in keywords[category]!) {
        if (question.contains(keyword)) {
          return PropertyFAQ.getResponse(category, property);
        }
      }
    }

    // Handle general questions
    if (question.contains('hello') || question.contains('hi')) {
      return 'Hello! I can answer questions about this property. What would you like to know?';
    } else if (question.contains('contact') || question.contains('owner')) {
      return 'You can contact ${property['userName'] ?? 'the owner'} through the chat feature.';
    } else if (question.contains('available') || question.contains('vacant')) {
      return property['isAvailable'] == true
          ? 'Yes, this property is currently available.'
          : 'This property is currently not available.';
    }

    return "I don't understand that question. Try asking about bedrooms, bathrooms, price, location, amenities, parking, or pets.";
  }
}
