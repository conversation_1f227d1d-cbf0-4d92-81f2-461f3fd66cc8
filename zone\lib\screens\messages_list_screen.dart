import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../firebase_auth.dart';
import '../models/firestore_collections.dart';
import 'chat_detail_screen.dart';
import 'package:logger/logger.dart';
import '../services/chat_service.dart';

class MessagesListScreen extends StatefulWidget {
  const MessagesListScreen({super.key});

  @override
  State<MessagesListScreen> createState() => _MessagesListScreenState();
}

class _MessagesListScreenState extends State<MessagesListScreen> {
  final Auth _auth = Auth();
  final Logger _logger = Logger();
  final ChatService _chatService = ChatService();
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  List<DocumentSnapshot> _conversations = [];
  List<DocumentSnapshot> _filteredConversations = [];
  String _searchQuery = '';
  late StreamSubscription<QuerySnapshot> _conversationsSubscription;
  int _selectedIndex = 1; // Set index to 1 for Messages tab

  @override
  void initState() {
    super.initState();
    _setupConversationsStream();
    setUserOnlineStatus(true);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _conversationsSubscription.cancel();
    setUserOnlineStatus(false);
    super.dispose();
  }

  void _setupConversationsStream() {
    setState(() {
      _isLoading = true;
    });

    try {
      _conversationsSubscription = _chatService.getConversationsStream().listen(
        (snapshot) {
          if (mounted) {
            setState(() {
              _conversations = snapshot.docs;
              _applySearch();
              _isLoading = false;
            });
          }
        },
        onError: (e) {
          _logger.e('Error in conversations stream: $e');
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        },
      );
    } catch (e) {
      _logger.e('Error setting up conversations stream: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applySearch() {
    if (_searchQuery.isEmpty) {
      _filteredConversations = List.from(_conversations);
    } else {
      _filteredConversations =
          _conversations.where((doc) {
            final data = doc.data() as Map<String, dynamic>;
            final agentName = data['agentName'] as String? ?? '';
            final propertyTitle = data['propertyTitle'] as String? ?? '';

            return agentName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                propertyTitle.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                );
          }).toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Messages',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(30),
              ),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  icon: Icon(Icons.search),
                  hintText: 'Search conversations...',
                  border: InputBorder.none,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applySearch();
                  });
                },
              ),
            ),
          ),

          // Conversation list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredConversations.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.chat_bubble_outline,
                            size: 80,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isNotEmpty
                                ? 'No conversations matching "₱{_searchController.text}"'
                                : 'No conversations yet',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (_searchQuery.isEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Start a conversation by contacting an agent\nfrom a property listing',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ],
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredConversations.length,
                      itemBuilder: (context, index) {
                        final conversation =
                            _filteredConversations[index].data()
                                as Map<String, dynamic>;
                        final String conversationId =
                            _filteredConversations[index].id;
                        final String agentName =
                            conversation['agentName'] ?? 'Agent';
                        final String propertyTitle =
                            conversation['propertyTitle'] ?? 'Property';
                        final String lastMessage =
                            conversation['lastMessage'] ?? '';
                        final bool unread =
                            (conversation['unreadCount'] ?? 0) > 0;
                        final Timestamp timestamp =
                            conversation['lastMessageTime'] ?? Timestamp.now();
                        final DateTime messageTime = timestamp.toDate();
                        final String formattedTime = _formatTime(messageTime);
                        final String agentId = conversation['agentId'] ?? '';
                        final String propertyId =
                            conversation['propertyId'] ?? '';
                        final List<dynamic> participants =
                            conversation['participants'] ?? [];

                        // Find the other participant (not the current user)
                        final String currentUserId =
                            _auth.currentUser?.uid ?? '';
                        final String otherUserId = participants.firstWhere(
                          (id) => id != currentUserId,
                          orElse: () => '',
                        );

                        return InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => ChatDetailScreen(
                                      conversationId: conversationId,
                                      agentId: agentId,
                                      agentName: agentName,
                                      propertyId: propertyId,
                                      propertyTitle: propertyTitle,
                                    ),
                              ),
                            ).then((_) => _setupConversationsStream());
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.grey[200]!,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                // Profile image
                                FutureBuilder<DocumentSnapshot>(
                                  future:
                                      FirebaseFirestore.instance
                                          .collection(
                                            FirestoreCollections.users,
                                          )
                                          .doc(otherUserId)
                                          .get(),
                                  builder: (context, snapshot) {
                                    bool isOnline = false;
                                    if (snapshot.hasData &&
                                        snapshot.data!.exists) {
                                      final userData =
                                          snapshot.data!.data()
                                              as Map<String, dynamic>?;
                                      isOnline = userData?['isOnline'] ?? false;
                                    }

                                    return Stack(
                                      children: [
                                        CircleAvatar(
                                          radius: 28,
                                          backgroundColor: Colors.grey[300],
                                          child: Text(
                                            agentName.isNotEmpty
                                                ? agentName[0].toUpperCase()
                                                : 'A',
                                            style: const TextStyle(
                                              fontSize: 22,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                        if (isOnline)
                                          Positioned(
                                            right: 0,
                                            bottom: 0,
                                            child: Container(
                                              width: 14,
                                              height: 14,
                                              decoration: BoxDecoration(
                                                color: Colors.green,
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: Colors.white,
                                                  width: 2,
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    );
                                  },
                                ),
                                const SizedBox(width: 16),
                                // Conversation details
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            agentName,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight:
                                                  unread
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                            ),
                                          ),
                                          Text(
                                            formattedTime,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              propertyTitle,
                                              style: TextStyle(
                                                fontSize: 13,
                                                color: Colors.blue[700],
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              lastMessage,
                                              style: TextStyle(
                                                color:
                                                    unread
                                                        ? Colors.black
                                                        : Colors.grey[600],
                                                fontWeight:
                                                    unread
                                                        ? FontWeight.w500
                                                        : FontWeight.normal,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          if (unread)
                                            Container(
                                              width: 10,
                                              height: 10,
                                              decoration: BoxDecoration(
                                                color: Colors.blue,
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  // Bottom navigation bar
  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      onTap: (index) {
        if (index != _selectedIndex) {
          setState(() {
            _selectedIndex = index;
          });

          if (index == 0) {
            Navigator.pushReplacementNamed(context, '/home');
          } else if (index == 1) {
            // Nothing to do, already on Messages
          } else if (index == 2) {
            Navigator.pushReplacementNamed(context, '/favorite');
          } else if (index == 3) {
            Navigator.pushReplacementNamed(context, '/profile');
          }
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: 'Messages',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.favorite_border),
          label: 'favorite',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      // Today, show time
      return DateFormat('h:mm a').format(dateTime);
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week, show day of week
      return DateFormat('EEEE').format(dateTime);
    } else {
      // More than a week ago, show date
      return DateFormat('MMM d').format(dateTime);
    }
  }

  Future<String> startConversation(String propertyId, String agentId) async {
    final currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null) return '';

    // Check if conversation already exists between these users for this property
    final querySnapshot =
        await FirebaseFirestore.instance
            .collection('conversations')
            .where('participants', arrayContains: currentUserId)
            .where('propertyId', isEqualTo: propertyId)
            .get();

    if (querySnapshot.docs.isNotEmpty) {
      // Conversation exists, return its ID
      return querySnapshot.docs.first.id;
    }

    // Get property and agent details
    final propertyDoc =
        await FirebaseFirestore.instance
            .collection(FirestoreCollections.properties)
            .doc(propertyId)
            .get();

    final agentDoc =
        await FirebaseFirestore.instance
            .collection(FirestoreCollections.users)
            .doc(agentId)
            .get();

    // Create new conversation
    final conversationRef =
        FirebaseFirestore.instance.collection('conversations').doc();
    await conversationRef.set({
      'participants': [currentUserId, agentId],
      'lastMessage': '',
      'lastMessageTime': FieldValue.serverTimestamp(),
      'unreadCount': 0,
      'propertyId': propertyId,
      'propertyTitle': propertyDoc.data()?['title'] ?? 'Property',
      'agentId': agentId,
      'agentName': agentDoc.data()?['firstName'] ?? 'Agent',
      'createdAt': FieldValue.serverTimestamp(),
    });

    return conversationRef.id;
  }

  Future<void> setUserOnlineStatus(bool isOnline) async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    await FirebaseFirestore.instance
        .collection(FirestoreCollections.users)
        .doc(userId)
        .update({
          'isOnline': isOnline,
          'lastActive': FieldValue.serverTimestamp(),
        });
  }
}
