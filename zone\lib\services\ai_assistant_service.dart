import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:logger/logger.dart';

/// Service class for handling AI assistant interactions
class AIAssistantService {
  final String? apiKey;
  final String baseUrl = 'https://api.openai.com/v1/chat/completions';
  final Logger _logger = Logger();

  AIAssistantService({this.apiKey});

  /// Get a response from the AI assistant for real estate inquiries
  Future<String> getPropertyAssistantResponse(
    String userMessage,
    Map<String, dynamic> propertyData,
  ) async {
    if (apiKey == null || apiKey!.isEmpty) {
      return 'AI Assistant is not configured. Please set up your OpenAI API key.';
    }

    try {
      // Format property data as context
      final String propertyContext = _formatPropertyContext(propertyData);

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content':
                  'You are a professional real estate assistant for Property Edge. '
                  'You help answer questions about properties, pricing, features, '
                  'and assist in communication between buyers/tenants and sellers/landlords. '
                  'Your responses should be concise, professional, helpful, and accurate. '
                  'Only use the property details provided to you. Do not make up information.',
            },
            {
              'role': 'user',
              'content':
                  'Here are the details of the property: $propertyContext',
            },
            {'role': 'user', 'content': userMessage},
          ],
          'temperature': 0.7,
          'max_tokens': 300,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'];
      } else {
        _logger.e(
          'AI Assistant error: ${response.statusCode} - ${response.body}',
        );
        return 'Sorry, I\'m having trouble connecting. Please try again later.';
      }
    } catch (e) {
      _logger.e('AI Assistant exception: $e');
      return 'An error occurred while processing your request. Please try again.';
    }
  }

  /// Format property data into a readable context string
  String _formatPropertyContext(Map<String, dynamic> propertyData) {
    final StringBuffer context = StringBuffer();

    // Add common property details
    context.writeln(
      'Property Title: ${propertyData['title'] ?? 'Not specified'}',
    );
    context.writeln('Location: ${propertyData['location'] ?? 'Not specified'}');

    // Handle price formatting
    var price = 'Not specified';
    if (propertyData['price'] is double) {
      price = '₱${(propertyData['price'] as double).toStringAsFixed(2)}';
    } else if (propertyData['price'] is int) {
      price =
          '₱${(propertyData['price'] as int).toDouble().toStringAsFixed(2)}';
    }
    context.writeln('Price: $price');

    // Add listing type (rent/sale)
    context.writeln(
      'Listing Type: ${propertyData['listingType'] == 'rent' ? 'For Rent' : 'For Sale'}',
    );

    // Add property type
    context.writeln(
      'Property Type: ${propertyData['propertyType'] ?? 'Not specified'}',
    );

    // Add features if available
    if (propertyData.containsKey('bedrooms') ||
        propertyData.containsKey('bathrooms') ||
        propertyData.containsKey('area')) {
      context.write('Features: ');
      if (propertyData.containsKey('bedrooms')) {
        context.write('${propertyData['bedrooms']} bedrooms, ');
      }
      if (propertyData.containsKey('bathrooms')) {
        context.write('${propertyData['bathrooms']} bathrooms, ');
      }
      if (propertyData.containsKey('area')) {
        var area = propertyData['area'];
        if (area is double) {
          context.write('${area.toStringAsFixed(0)} sq ft');
        } else if (area is int) {
          context.write('$area sq ft');
        } else {
          context.write('Area not specified');
        }
      }
      context.writeln();
    }

    // Add description if available
    if (propertyData.containsKey('description') &&
        propertyData['description'] != null) {
      context.writeln('Description: ${propertyData['description']}');
    }

    return context.toString();
  }
}
