import 'package:flutter/material.dart';
import '../widgets/property_detail_card.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

class PropertyDetailScreen extends StatefulWidget {
  final Map<String, dynamic> property;

  const PropertyDetailScreen({super.key, required this.property});

  @override
  State<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends State<PropertyDetailScreen> {
  final Logger _logger = Logger();

  @override
  void initState() {
    super.initState();
    _trackPropertyView();
  }

  // Track that the user viewed this property
  Future<void> _trackPropertyView() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    final String propertyId = widget.property['id'];

    if (currentUser != null && propertyId.isNotEmpty) {
      try {
        // Simpler query - just check for matches on userId and propertyId
        final recentViews =
            await FirebaseFirestore.instance
                .collection('views')
                .where('userId', isEqualTo: currentUser.uid)
                .where('propertyId', isEqualTo: propertyId)
                .get();

        // Only record a new view if no previous views or if last view is older than 1 hour
        bool shouldRecordView = true;

        if (recentViews.docs.isNotEmpty) {
          // Check timestamps manually after fetching the data
          for (var doc in recentViews.docs) {
            final viewedAt = doc['viewedAt'] as Timestamp?;
            if (viewedAt != null) {
              final oneHourAgo = DateTime.now().subtract(
                const Duration(hours: 1),
              );
              if (viewedAt.toDate().isAfter(oneHourAgo)) {
                shouldRecordView = false;
                break;
              }
            }
          }
        }

        if (shouldRecordView) {
          // Add to views collection
          await FirebaseFirestore.instance.collection('views').add({
            'userId': currentUser.uid,
            'propertyId': propertyId,
            'viewedAt': FieldValue.serverTimestamp(),
          });

          // Increment property views counter
          await FirebaseFirestore.instance
              .collection('properties')
              .doc(propertyId)
              .update({'views': FieldValue.increment(1)});
        }
      } catch (e) {
        _logger.e('Error tracking property view: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: PropertyDetailCard(property: widget.property),
      ),
    );
  }
}
