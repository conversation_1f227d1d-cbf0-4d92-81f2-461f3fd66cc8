import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../firebase_auth.dart';
import '../models/firestore_collections.dart';
import 'package:logger/logger.dart';
import '../services/chat_service.dart';
import '../screens/property_detail_screen.dart';
import '../widgets/ai_assistant_chat.dart';
import '../config/app_config.dart';

class ChatDetailScreen extends StatefulWidget {
  final String conversationId;
  final String agentId;
  final String agentName;
  final String propertyId;
  final String propertyTitle;

  const ChatDetailScreen({
    super.key,
    required this.conversationId,
    required this.agentId,
    required this.agentName,
    required this.propertyId,
    required this.propertyTitle,
  });

  @override
  State<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final Auth _auth = Auth();
  final Logger _logger = Logger();
  final ChatService _chatService = ChatService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isAgentOnline = false;
  bool _isSending = false;
  bool _useAIAssistant = false;
  late Stream<QuerySnapshot> _messagesStream;
  Map<String, dynamic>? _propertyData;

  @override
  void initState() {
    super.initState();
    _setupMessagesStream();
    _loadPropertyData();
    _markConversationAsRead();
    _checkAgentOnlineStatus();

    // Make sure AI assistant is disabled by default
    _useAIAssistant = false; // Explicitly set to false
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupMessagesStream() {
    _messagesStream = _chatService.getMessagesStream(widget.conversationId);
  }

  Future<void> _loadPropertyData() async {
    try {
      final propertyDoc =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .doc(widget.propertyId)
              .get();

      if (propertyDoc.exists && mounted) {
        setState(() {
          _propertyData = propertyDoc.data();
        });
      }
    } catch (e) {
      _logger.e('Error loading property data: $e');
    }
  }

  Future<void> _markConversationAsRead() async {
    try {
      await _chatService.markConversationAsRead(widget.conversationId);
    } catch (e) {
      _logger.e('Error marking conversation as read: $e');
    }
  }

  Future<void> _checkAgentOnlineStatus() async {
    try {
      // Set up stream listener for agent online status
      _chatService.userOnlineStatus(widget.agentId).listen((isOnline) {
        if (mounted) {
          setState(() {
            _isAgentOnline = isOnline;
          });
        }
      });
    } catch (e) {
      _logger.e('Error checking agent online status: $e');
    }
  }

  Future<void> _sendMessage() async {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    try {
      final success = await _chatService.sendMessage(
        widget.conversationId,
        messageText,
      );

      if (success) {
        // Clear input field
        _messageController.clear();

        // Scroll to bottom after message is sent
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollToBottom();
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send message. Please try again.'),
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Error sending message: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to send message: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = _auth.currentUser?.uid;

    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        elevation: 1,
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.blue[100],
              child: Text(
                widget.agentName.isNotEmpty
                    ? widget.agentName[0].toUpperCase()
                    : 'A',
                style: TextStyle(
                  color: Colors.blue[800],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(widget.agentName, style: const TextStyle(fontSize: 16)),
                Text(
                  _isAgentOnline ? 'Online' : 'Offline',
                  style: TextStyle(
                    fontSize: 12,
                    color: _isAgentOnline ? Colors.green : Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          // AI Assistant toggle button
          if (AppConfig.aiEnabled)
            IconButton(
              icon: Icon(
                Icons.smart_toy_outlined,
                color: _useAIAssistant ? Colors.blue : Colors.grey,
              ),
              tooltip:
                  _useAIAssistant
                      ? 'Disable AI Assistant'
                      : 'Enable AI Assistant',
              onPressed: () {
                setState(() {
                  _useAIAssistant = !_useAIAssistant;
                });

                // Show a confirmation message when toggled
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      _useAIAssistant
                          ? 'AI Assistant enabled - ask property questions'
                          : 'AI Assistant disabled',
                    ),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // Show property details
              _showPropertyDetails(context);
            },
          ),
        ],
      ),
      body:
          _propertyData == null
              ? const Center(child: CircularProgressIndicator())
              : _useAIAssistant &&
                  AppConfig.openAIApiKey != null &&
                  AppConfig.openAIApiKey!.isNotEmpty
              ? AIAssistantChat(
                property: {
                  ..._propertyData!,
                  'id': widget.propertyId,
                  'title': widget.propertyTitle,
                },
                apiKey: AppConfig.openAIApiKey!,
                isEnabled: _useAIAssistant,
                onToggleAI: (value) {
                  setState(() {
                    _useAIAssistant = value;
                  });
                },
              )
              : Column(
                children: [
                  // Property card at the top
                  if (_propertyData != null) _buildPropertyCard(),

                  // Messages list
                  Expanded(
                    child: StreamBuilder<QuerySnapshot>(
                      stream: _messagesStream,
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        if (snapshot.hasError) {
                          return Center(
                            child: Text('Error: ${snapshot.error}'),
                          );
                        }

                        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.chat_bubble_outline,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No messages yet',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Send a message to start the conversation',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        // Scroll to bottom when messages are loaded
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _scrollToBottom();
                        });

                        final messages = snapshot.data!.docs;

                        return ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: messages.length,
                          itemBuilder: (context, index) {
                            final message =
                                messages[index].data() as Map<String, dynamic>;
                            final String senderId = message['senderId'] ?? '';
                            final bool isCurrentUser =
                                senderId == currentUserId;
                            final String text = message['text'] ?? '';
                            final Timestamp? timestamp =
                                message['timestamp'] as Timestamp?;
                            final DateTime dateTime =
                                timestamp?.toDate() ?? DateTime.now();
                            final String timeString = DateFormat(
                              'h:mm a',
                            ).format(dateTime);

                            return _buildMessageBubble(
                              text: text,
                              time: timeString,
                              isCurrentUser: isCurrentUser,
                              showTime: true,
                            );
                          },
                        );
                      },
                    ),
                  ),

                  // Message input field
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(
                            red: 128,
                            green: 128,
                            blue: 128,
                            alpha: 0.1,
                          ),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, -1),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Attachment button
                        IconButton(
                          icon: Icon(
                            Icons.attach_file,
                            color: Colors.grey[600],
                          ),
                          onPressed: () {
                            // Show attachment options
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Attachment functionality coming soon',
                                ),
                              ),
                            );
                          },
                        ),
                        // Text input
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            decoration: InputDecoration(
                              hintText: 'Type a message...',
                              hintStyle: TextStyle(color: Colors.grey[500]),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(24),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.grey[100],
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 10,
                              ),
                            ),
                            textCapitalization: TextCapitalization.sentences,
                            keyboardType: TextInputType.multiline,
                            maxLines: null,
                            textInputAction: TextInputAction.send,
                            onSubmitted: (_) => _sendMessage(),
                          ),
                        ),
                        // Send button
                        IconButton(
                          icon: Icon(
                            Icons.send,
                            color:
                                _messageController.text.trim().isEmpty
                                    ? Colors.grey[400]
                                    : Colors.blue,
                          ),
                          onPressed: _sendMessage,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildPropertyCard() {
    final List<dynamic> images = _propertyData?['images'] ?? [];
    final String imageUrl = images.isNotEmpty ? images[0] : '';
    final double price;

    if (_propertyData != null && _propertyData!['price'] is double) {
      price = _propertyData!['price'] as double;
    } else if (_propertyData != null && _propertyData!['price'] is int) {
      price = (_propertyData!['price'] as int).toDouble();
    } else {
      price = 0.0;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              red: 128,
              green: 128,
              blue: 128,
              alpha: 0.1,
            ),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Property image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[200],
              image:
                  imageUrl.isNotEmpty
                      ? DecorationImage(
                        image: NetworkImage(imageUrl),
                        fit: BoxFit.cover,
                      )
                      : null,
            ),
            child:
                imageUrl.isEmpty
                    ? const Icon(Icons.home, color: Colors.grey)
                    : null,
          ),
          const SizedBox(width: 12),
          // Property details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.propertyTitle,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  _propertyData?['location'] ?? 'Location unavailable',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '₱${price.toStringAsFixed(2)}${_propertyData != null && _propertyData!['listingType'] == 'rent' ? '/month' : ''}',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble({
    required String text,
    required String time,
    required bool isCurrentUser,
    required bool showTime,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isCurrentUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[300],
              child: Text(
                widget.agentName.isNotEmpty
                    ? widget.agentName[0].toUpperCase()
                    : 'A',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isCurrentUser ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(
                      red: 128,
                      green: 128,
                      blue: 128,
                      alpha: 0.1,
                    ),
                    spreadRadius: 1,
                    blurRadius: 1,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    style: TextStyle(
                      color: isCurrentUser ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (showTime)
                    Text(
                      time,
                      style: TextStyle(
                        fontSize: 10,
                        color:
                            isCurrentUser
                                ? Colors.white.withValues(
                                  red: 255,
                                  green: 255,
                                  blue: 255,
                                  alpha: 0.7,
                                )
                                : Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
          ),
          if (isCurrentUser) const SizedBox(width: 8),
        ],
      ),
    );
  }

  void _showPropertyDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Property Details',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              if (_propertyData != null) ...[
                ListTile(
                  leading: const Icon(Icons.home),
                  title: Text(widget.propertyTitle),
                  subtitle: Text(
                    '₱${_propertyData?['propertyType'] ?? 'Property'}',
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.location_on),
                  title: Text(
                    _propertyData?['location'] ?? 'Location unavailable',
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.attach_money),
                  title: Text(
                    'Price: ₱${(_propertyData?['price'] ?? 0).toStringAsFixed(2)}',
                  ),
                  subtitle: Text(
                    _propertyData?['listingType'] == 'rent'
                        ? 'For Rent'
                        : 'For Sale',
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.person),
                  title: Text('Listed by: ${widget.agentName}'),
                ),
              ] else ...[
                const Center(child: Text('Loading property details...')),
              ],
              const SizedBox(height: 16),
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Navigate to property details
                    if (_propertyData != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => PropertyDetailScreen(
                                property: {
                                  ..._propertyData!,
                                  'id': widget.propertyId,
                                },
                              ),
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                  ),
                  child: const Text('View Full Details'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
