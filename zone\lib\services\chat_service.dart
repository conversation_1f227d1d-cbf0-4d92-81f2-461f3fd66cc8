import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/firestore_collections.dart';
import 'package:logger/logger.dart';

/// Service class to manage all chat operations with Firebase
class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Logger _logger = Logger();

  /// Get current user id
  String? get currentUserId => _auth.currentUser?.uid;

  /// Start or get an existing conversation
  Future<String> startConversation(
    String propertyId,
    String agentId,
    String? propertyTitle,
    String? agentName,
  ) async {
    if (currentUserId == null) return '';

    try {
      // Check if conversation already exists
      final querySnapshot =
          await _firestore
              .collection('conversations')
              .where('participants', arrayContains: currentUserId)
              .where('propertyId', isEqualTo: propertyId)
              .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.id; // Return existing conversation
      }

      // If we need property data but don't have it
      if (propertyTitle == null) {
        final propertyDoc =
            await _firestore
                .collection(FirestoreCollections.properties)
                .doc(propertyId)
                .get();
        propertyTitle = propertyDoc.data()?['title'] ?? 'Property';
      }

      // If we need agent data but don't have it
      if (agentName == null) {
        final agentDoc =
            await _firestore
                .collection(FirestoreCollections.users)
                .doc(agentId)
                .get();

        final firstName = agentDoc.data()?['firstName'] ?? '';
        final lastName = agentDoc.data()?['lastName'] ?? '';
        agentName = '$firstName $lastName'.trim();
        if (agentName.isEmpty) agentName = 'Agent';
      }

      // Create new conversation
      final conversationRef = _firestore.collection('conversations').doc();
      await conversationRef.set({
        'participants': [currentUserId, agentId],
        'lastMessage': '',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'unreadCount': 0,
        'propertyId': propertyId,
        'propertyTitle': propertyTitle,
        'agentId': agentId,
        'agentName': agentName,
        'createdAt': FieldValue.serverTimestamp(),
      });

      return conversationRef.id;
    } catch (e) {
      _logger.e('Error starting conversation: $e');
      return '';
    }
  }

  /// Send message in a conversation
  Future<bool> sendMessage(String conversationId, String text) async {
    if (currentUserId == null) return false;

    try {
      final messageData = {
        'senderId': currentUserId,
        'text': text,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      };

      // Get conversation data to check if current user is the agent
      final conversationDoc =
          await _firestore
              .collection('conversations')
              .doc(conversationId)
              .get();

      final String agentId = conversationDoc.data()?['agentId'] ?? '';

      // Add message to conversation
      await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .add(messageData);

      // Update conversation metadata
      await _firestore.collection('conversations').doc(conversationId).update({
        'lastMessage': text,
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastSenderId': currentUserId,
        // Increment unread count for recipient
        'unreadCount': currentUserId != agentId ? FieldValue.increment(1) : 0,
      });

      return true;
    } catch (e) {
      _logger.e('Error sending message: $e');
      return false;
    }
  }

  /// Mark a conversation as read
  Future<void> markConversationAsRead(String conversationId) async {
    if (currentUserId == null) return;

    try {
      final conversationDoc =
          await _firestore
              .collection('conversations')
              .doc(conversationId)
              .get();

      // Only mark as read if the current user is not the last sender
      final String lastSenderId = conversationDoc.data()?['lastSenderId'] ?? '';
      if (lastSenderId != currentUserId) {
        await _firestore.collection('conversations').doc(conversationId).update(
          {'unreadCount': 0},
        );

        // Mark individual messages as read
        final messagesQuery =
            await _firestore
                .collection('conversations')
                .doc(conversationId)
                .collection('messages')
                .where('isRead', isEqualTo: false)
                .where('senderId', isNotEqualTo: currentUserId)
                .get();

        final batch = _firestore.batch();
        for (final doc in messagesQuery.docs) {
          batch.update(doc.reference, {'isRead': true});
        }
        await batch.commit();
      }
    } catch (e) {
      _logger.e('Error marking conversation as read: $e');
    }
  }

  /// Get user online status
  Stream<bool> userOnlineStatus(String userId) {
    return _firestore
        .collection(FirestoreCollections.users)
        .doc(userId)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.exists ? (snapshot.data()?['isOnline'] ?? false) : false,
        );
  }

  /// Set current user's online status
  Future<void> setUserOnlineStatus(bool isOnline) async {
    if (currentUserId == null) return;

    try {
      await _firestore
          .collection(FirestoreCollections.users)
          .doc(currentUserId)
          .update({
            'isOnline': isOnline,
            'lastActive': FieldValue.serverTimestamp(),
          });
    } catch (e) {
      _logger.e('Error updating online status: $e');
    }
  }

  /// Get conversation stream
  Stream<QuerySnapshot> getConversationsStream() {
    if (currentUserId == null) {
      return Stream.empty();
    }

    return _firestore
        .collection('conversations')
        .where('participants', arrayContains: currentUserId)
        .orderBy('lastMessageTime', descending: true)
        .snapshots();
  }

  /// Get messages stream for a conversation
  Stream<QuerySnapshot> getMessagesStream(String conversationId) {
    return _firestore
        .collection('conversations')
        .doc(conversationId)
        .collection('messages')
        .orderBy('timestamp', descending: false)
        .snapshots();
  }

  /// Delete conversation
  Future<void> deleteConversation(String conversationId) async {
    if (currentUserId == null) return;

    try {
      // First delete all messages
      final messagesQuery =
          await _firestore
              .collection('conversations')
              .doc(conversationId)
              .collection('messages')
              .get();

      final batch = _firestore.batch();
      for (final doc in messagesQuery.docs) {
        batch.delete(doc.reference);
      }

      // Then delete the conversation
      batch.delete(_firestore.collection('conversations').doc(conversationId));
      await batch.commit();
    } catch (e) {
      _logger.e('Error deleting conversation: $e');
    }
  }
}
