# Property Edge AI Assistant Integration

This document provides instructions for setting up and using the AI assistant feature in the Property Edge application.

## Overview

The Property Edge AI Assistant uses OpenAI's API to enhance chat interactions between buyers/tenants and sellers/landlords. The assistant can answer questions about properties, provide insights about pricing, features, and help facilitate conversations.

## Setup Instructions

### 1. Get an OpenAI API Key

1. Visit [OpenAI's platform](https://platform.openai.com/)
2. Create an account or sign in
3. Go to API Keys section
4. Create a new API key
5. Copy the key (you will need it in the next step)

### 2. Configure Your Environment

1. Open the `.env` file in the root of the project
2. Replace `your_openai_api_key_here` with your actual OpenAI API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key
   ```
3. Save the file

### 3. Run the Application

Make sure you have all dependencies installed:

```
flutter pub get
```

Then run the application:

```
flutter run
```

## Using the AI Assistant

1. Open a chat conversation with a property seller/landlord
2. Click the robot icon in the top right corner to enable the AI assistant
3. Use the AI assistant to ask questions about the property
4. Toggle back to the regular chat mode when you want to talk directly to the seller/landlord

## Features

- **Property Information**: Ask about price, features, location, etc.
- **Negotiation Assistance**: Get help with understanding fair pricing
- **24/7 Availability**: Get instant responses even when the seller is offline
- **Context-Aware**: The assistant has access to all property details

## Maintenance

- Update your OpenAI API key in the `.env` file if it expires
- The AI assistant uses GPT-3.5-turbo by default, but you can modify the model in `ai_assistant_service.dart`

## Troubleshooting

- If the AI assistant is not appearing, check that your API key is correctly set in the `.env` file
- If responses are slow, check your internet connection
- If you receive error messages, check the logs for detailed error information

## Security Notes

- Your OpenAI API key should be kept secret and not shared
- The `.env` file is included in `.gitignore` to prevent accidental commits
- All communication with OpenAI is done securely via HTTPS 