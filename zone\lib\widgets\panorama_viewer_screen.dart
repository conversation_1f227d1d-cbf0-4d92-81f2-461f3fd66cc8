import 'package:flutter/material.dart';
import 'package:panorama_viewer/panorama_viewer.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../services/storage_service.dart';
import 'package:logger/logger.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'dart:math' as math;

class PanoramaViewerScreen extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;

  const PanoramaViewerScreen({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
  });

  @override
  State<PanoramaViewerScreen> createState() => _PanoramaViewerScreenState();
}

class _PanoramaViewerScreenState extends State<PanoramaViewerScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = 'Failed to load panoramic image';
  late int _currentIndex;
  final Logger _logger = Logger();
  final StorageService _storageService = StorageService();

  // Gyroscope control variables
  bool _isGyroEnabled = false;
  final double _sensorSensitivity = 0.5;
  double _longitude = 0.0;
  double _latitude = 0.0;

  // Controller for panorama view
  final GlobalKey _panoramaKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;

    // Check if device has gyroscope sensor
    _checkGyroscopeAvailability();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _checkGyroscopeAvailability() async {
    try {
      gyroscopeEvents.first
          .timeout(
            const Duration(milliseconds: 500),
            onTimeout: () => throw Exception('Gyroscope not available'),
          )
          .then((_) {
            if (mounted) {
              setState(() {
                _isGyroEnabled = true;
              });
            }
          })
          .catchError((e) {
            _logger.i('Gyroscope not available: $e');
          });
    } catch (e) {
      _logger.i('Error checking gyroscope: $e');
    }
  }

  void _nextImage() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentIndex = (_currentIndex + 1) % widget.imageUrls.length;
    });
  }

  void _previousImage() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentIndex =
          (_currentIndex - 1 + widget.imageUrls.length) %
          widget.imageUrls.length;
    });
  }

  Future<String> _getImageUrl() async {
    if (_storageService.isFirebaseStorageUrl(widget.imageUrls[_currentIndex])) {
      try {
        final String path = _storageService.extractPathFromUrl(
          widget.imageUrls[_currentIndex],
        );
        if (path.isNotEmpty) {
          return await _storageService.getFreshDownloadUrl(path);
        }
      } catch (e) {
        _logger.e('Error getting fresh URL: $e');
      }
    }
    return widget.imageUrls[_currentIndex];
  }

  void _toggleGyroscope() {
    setState(() {
      _isGyroEnabled = !_isGyroEnabled;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isGyroEnabled
              ? 'Gyroscope control enabled'
              : 'Touch control enabled',
        ),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          if (!_hasError)
            FutureBuilder<String>(
              future: _getImageUrl(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: SpinKitSpinningCircle(
                      color: Colors.white,
                      size: 50.0,
                    ),
                  );
                } else if (snapshot.hasError) {
                  _logger.e('URL refresh error: ${snapshot.error}');
                  return _buildErrorWidget('Error refreshing image URL');
                } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return _buildErrorWidget('Invalid image URL');
                }

                final imageUrl = snapshot.data!;

                if (_isGyroEnabled) {
                  return StreamBuilder<GyroscopeEvent>(
                    stream: gyroscopeEvents,
                    builder: (context, gyroSnapshot) {
                      if (gyroSnapshot.hasData) {
                        // Update longitude and latitude based on gyroscope data
                        _longitude += gyroSnapshot.data!.y * _sensorSensitivity;
                        _latitude += gyroSnapshot.data!.x * _sensorSensitivity;

                        // Limit latitude to avoid flipping
                        _latitude = _latitude.clamp(-math.pi / 2, math.pi / 2);
                      }

                      return PanoramaViewer(
                        key: _panoramaKey,
                        sensitivity:
                            _isGyroEnabled
                                ? 0
                                : 3.0, // Disable touch sensitivity when gyro is enabled
                        animSpeed: 0.5,
                        longitude: _longitude,
                        latitude: _latitude,
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) {
                              if (_isLoading && mounted) {
                                setState(() {
                                  _isLoading = false;
                                });
                              }
                              return child;
                            }
                            return Container();
                          },
                          errorBuilder: (context, error, stackTrace) {
                            _logger.e('Image error: $error');
                            if (!_hasError && mounted) {
                              setState(() {
                                _hasError = true;
                                _errorMessage =
                                    'Failed to load image: ${error.toString().contains('403') ? 'Access denied' : 'Unknown error'}';
                              });
                            }
                            return Container();
                          },
                        ),
                      );
                    },
                  );
                } else {
                  return PanoramaViewer(
                    key: _panoramaKey,
                    sensitivity: 3.0,
                    animSpeed: 0.5,
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) {
                          if (_isLoading && mounted) {
                            setState(() {
                              _isLoading = false;
                            });
                          }
                          return child;
                        }
                        return Container();
                      },
                      errorBuilder: (context, error, stackTrace) {
                        _logger.e('Image error: $error');
                        if (!_hasError && mounted) {
                          setState(() {
                            _hasError = true;
                            _errorMessage =
                                'Failed to load image: ${error.toString().contains('403') ? 'Access denied' : 'Unknown error'}';
                          });
                        }
                        return Container();
                      },
                    ),
                  );
                }
              },
            ),
          if (_isLoading && !_hasError)
            const Center(
              child: SpinKitSpinningCircle(color: Colors.white, size: 50.0),
            ),
          if (_hasError) _buildErrorWidget(_errorMessage),

          // Help overlay button (top-left)
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(
                  red: 0,
                  green: 0,
                  blue: 0,
                  alpha: 178,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(Icons.help_outline, color: Colors.white),
                onPressed: () {
                  _showHelpDialog();
                },
              ),
            ),
          ),

          // Gyroscope toggle button (top-right)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(
                  red: 0,
                  green: 0,
                  blue: 0,
                  alpha: 178,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: Icon(
                  _isGyroEnabled ? Icons.screen_rotation : Icons.touch_app,
                  color: Colors.white,
                ),
                onPressed: _toggleGyroscope,
              ),
            ),
          ),

          // Navigation buttons (only show if there are multiple images)
          if (widget.imageUrls.length > 1)
            Positioned(
              left: 16,
              top: 0,
              bottom: 0,
              child: Center(
                child: FloatingActionButton(
                  heroTag: "prevBtn",
                  mini: true,
                  onPressed: _previousImage,
                  backgroundColor: Colors.white.withValues(
                    red: 255,
                    green: 255,
                    blue: 255,
                    alpha: 178,
                  ),
                  child: const Icon(Icons.arrow_back_ios, color: Colors.black),
                ),
              ),
            ),
          if (widget.imageUrls.length > 1)
            Positioned(
              right: 16,
              top: 0,
              bottom: 0,
              child: Center(
                child: FloatingActionButton(
                  heroTag: "nextBtn",
                  mini: true,
                  onPressed: _nextImage,
                  backgroundColor: Colors.white.withValues(
                    red: 255,
                    green: 255,
                    blue: 255,
                    alpha: 178,
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          // Image counter
          if (widget.imageUrls.length > 1)
            Positioned(
              top: 16,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(
                      red: 0,
                      green: 0,
                      blue: 0,
                      alpha: 178,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${_currentIndex + 1}/${widget.imageUrls.length}',
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.of(context).pop(),
        backgroundColor: Colors.white.withValues(
          red: 255,
          green: 255,
          blue: 255,
          alpha: 178,
        ),
        child: const Icon(Icons.close, color: Colors.black),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('360° Viewer Help'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHelpItem(
                  Icons.touch_app,
                  'Touch and drag to look around',
                ),
                const SizedBox(height: 8),
                _buildHelpItem(
                  Icons.screen_rotation,
                  'Toggle motion control (move phone to look around)',
                ),
                const SizedBox(height: 8),
                if (widget.imageUrls.length > 1)
                  _buildHelpItem(
                    Icons.arrow_forward_ios,
                    'Swipe or use arrows to view different panoramas',
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Got it!'),
              ),
            ],
          ),
    );
  }

  Widget _buildHelpItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 20),
        const SizedBox(width: 8),
        Expanded(child: Text(text)),
      ],
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.white, size: 60),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              if (mounted) {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
