import 'package:flutter/material.dart';
import 'panorama_viewer_screen.dart';
import '../services/chat_service.dart';
import '../screens/chat_detail_screen.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/storage_service.dart';
import 'package:logger/logger.dart';

class PropertyDetailCard extends StatefulWidget {
  final Map<String, dynamic> property;

  const PropertyDetailCard({super.key, required this.property});

  @override
  State<PropertyDetailCard> createState() => _PropertyDetailCardState();
}

class _PropertyDetailCardState extends State<PropertyDetailCard> {
  bool _isFavorite = false;
  final PageController _imageController = PageController();
  int _currentImageIndex = 0;
  final StorageService _storageService = StorageService();
  final Logger _logger = Logger();
  List<String> _refreshedPanoramaUrls = [];
  bool _isLoadingPanoramaUrls = true;

  @override
  void dispose() {
    _imageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _checkIfFavorite();
    _refreshPanoramaUrls();
  }

  Future<void> _refreshPanoramaUrls() async {
    // Get panoramic image URLs (support both legacy and new format)
    final List<String> panoramaUrls = [];
    if (widget.property['panoramaUrls'] != null &&
        widget.property['panoramaUrls'] is List) {
      // New format with multiple panorama images
      panoramaUrls.addAll(
        (widget.property['panoramaUrls'] as List).cast<String>(),
      );
    } else if (widget.property['panoramaUrl'] != null &&
        widget.property['panoramaUrl'] is String) {
      // Legacy format with single panorama image
      panoramaUrls.add(widget.property['panoramaUrl'] as String);
    }

    if (panoramaUrls.isNotEmpty) {
      try {
        final refreshed = await _storageService.refreshStorageUrls(
          panoramaUrls,
        );
        if (mounted) {
          setState(() {
            _refreshedPanoramaUrls = refreshed;
            _isLoadingPanoramaUrls = false;
          });
        }
      } catch (e) {
        _logger.e('Error refreshing panorama URLs: $e');
        if (mounted) {
          setState(() {
            _refreshedPanoramaUrls = panoramaUrls; // Use original URLs on error
            _isLoadingPanoramaUrls = false;
          });
        }
      }
    } else {
      setState(() {
        _isLoadingPanoramaUrls = false;
      });
    }
  }

  Future<void> _checkIfFavorite() async {
    final userId = FirebaseAuth.instance.currentUser?.uid;
    final propertyId = widget.property['id'];

    if (userId != null && propertyId != null) {
      try {
        final query =
            await FirebaseFirestore.instance
                .collection('favorites')
                .where('userId', isEqualTo: userId)
                .where('propertyId', isEqualTo: propertyId)
                .get();

        if (mounted) {
          setState(() {
            _isFavorite = query.docs.isNotEmpty;
          });
        }
      } catch (e) {
        // Handle error silently
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String title = widget.property['title'] ?? 'No Title';
    final String location = widget.property['location'] ?? 'No Location';
    final String description =
        widget.property['description'] ?? 'No description available.';
    final double price =
        widget.property['price'] is double
            ? widget.property['price']
            : widget.property['price'] is int
            ? (widget.property['price'] as int).toDouble()
            : 0.0;
    final List<dynamic> images = widget.property['images'] ?? [];

    // Get panoramic image URLs (support both legacy and new format)
    final List<String> panoramaUrls = [];
    if (widget.property['panoramaUrls'] != null &&
        widget.property['panoramaUrls'] is List) {
      // New format with multiple panorama images
      panoramaUrls.addAll(
        (widget.property['panoramaUrls'] as List).cast<String>(),
      );
    } else if (widget.property['panoramaUrl'] != null &&
        widget.property['panoramaUrl'] is String) {
      // Legacy format with single panorama image
      panoramaUrls.add(widget.property['panoramaUrl'] as String);
    }

    final bool hasPanorama = panoramaUrls.isNotEmpty;
    final int bedrooms = widget.property['bedrooms'] ?? 0;
    final int bathrooms = widget.property['bathrooms'] ?? 0;
    final double area =
        widget.property['area'] is double
            ? widget.property['area']
            : widget.property['area'] is int
            ? (widget.property['area'] as int).toDouble()
            : 0.0;
    final String sellerName = widget.property['userName'] ?? 'Unknown Agent';
    final String sellerEmail = widget.property['userEmail'] ?? '';
    final String propertyType = widget.property['propertyType'] ?? 'Unknown';
    final String listingType = widget.property['listingType'] ?? 'Unknown';
    final bool isAvailable = widget.property['isAvailable'] ?? false;
    // ignore: unused_local_variable
    final int views = widget.property['views'] ?? 0;
    final int inquiries = widget.property['inquiries'] ?? 0;
    final DateTime listedDate =
        widget.property['listedDate'] != null
            ? (widget.property['listedDate'] is Timestamp
                ? (widget.property['listedDate'] as Timestamp).toDate()
                : widget.property['listedDate'])
            : DateTime.now();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image carousel with indicators
        Stack(
          children: [
            SizedBox(
              height: 250,
              child:
                  images.isNotEmpty
                      ? PageView.builder(
                        controller: _imageController,
                        itemCount: images.length,
                        onPageChanged: (index) {
                          setState(() {
                            _currentImageIndex = index;
                          });
                        },
                        itemBuilder: (context, index) {
                          return Image.network(
                            images[index],
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              );
                            },
                          );
                        },
                      )
                      : Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(
                            Icons.image,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                      ),
            ),
            // Back button
            Positioned(
              top: 17,
              left: 17,

              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            // Favorite button
            Positioned(
              top: 17,
              right: 17,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromARGB(
                    255,
                    255,
                    248,
                    248,
                  ).withValues(red: 0, green: 0, blue: 0, alpha: 0),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(
                    _isFavorite ? Icons.favorite : Icons.favorite_border,
                    color:
                        _isFavorite
                            ? Colors.red
                            : Colors.white.withValues(
                              red: 255,
                              green: 255,
                              blue: 255,
                              alpha: 178,
                            ),
                  ),
                  onPressed: () async {
                    // Toggle local state first
                    setState(() {
                      _isFavorite = !_isFavorite;
                    });

                    // Show feedback immediately
                    final bool isFavorite =
                        _isFavorite; // Capture current state
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          isFavorite
                              ? 'Added to favorites'
                              : 'Removed from favorites',
                        ),
                        duration: const Duration(seconds: 1),
                      ),
                    );

                    // Save to database (after UI feedback)
                    final userId = FirebaseAuth.instance.currentUser?.uid;
                    final propertyId = widget.property['id'];

                    if (userId != null && propertyId != null) {
                      try {
                        // Check if it's already in favorites
                        final query =
                            await FirebaseFirestore.instance
                                .collection('favorites')
                                .where('userId', isEqualTo: userId)
                                .where('propertyId', isEqualTo: propertyId)
                                .get();

                        if (isFavorite && query.docs.isEmpty) {
                          // Add to favorites
                          await FirebaseFirestore.instance
                              .collection('favorites')
                              .add({
                                'userId': userId,
                                'propertyId': propertyId,
                                'createdAt': FieldValue.serverTimestamp(),
                              });
                        } else if (!isFavorite && query.docs.isNotEmpty) {
                          // Remove from favorites
                          await FirebaseFirestore.instance
                              .collection('favorites')
                              .doc(query.docs.first.id)
                              .delete();
                        }
                      } catch (e) {
                        // Silently handle error or show error message if widget is still mounted
                      }
                    }
                  },
                ),
              ),
            ),
            // 360° tour button
            if (hasPanorama)
              Positioned(
                right: 16,
                bottom: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.transparent.withValues(
                      red: 0,
                      green: 0,
                      blue: 0,
                      alpha: 0,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Material(
                    color: const Color.fromARGB(0, 4, 133, 239),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => PanoramaViewerScreen(
                                  imageUrls:
                                      _isLoadingPanoramaUrls
                                          ? panoramaUrls // Use original URLs while refreshing
                                          : _refreshedPanoramaUrls.isEmpty
                                          ? panoramaUrls // Fallback to original if refresh failed
                                          : _refreshedPanoramaUrls,
                                  initialIndex: 0,
                                ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                shape: BoxShape.circle,
                              ),
                              padding: const EdgeInsets.all(8),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Icon(
                                    Icons.panorama_horizontal,
                                    color: Colors.blue[700],
                                    size: 26,
                                  ),
                                  if (panoramaUrls.length > 1)
                                    Positioned(
                                      right: -2,
                                      top: -2,
                                      child: Container(
                                        width: 18,
                                        height: 18,
                                        decoration: BoxDecoration(
                                          color: Colors.blue.shade600,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '${panoramaUrls.length}',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'View in 360°',
                                  style: TextStyle(
                                    color: Colors.blue[700],
                                    fontWeight: FontWeight.bold,
                                    fontSize: 15,
                                  ),
                                ),
                                Text(
                                  'Interactive tour',
                                  style: TextStyle(
                                    color: Colors.blue[400],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            // Page indicator
            if (images.length > 1)
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(images.length, (index) {
                    return Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            _currentImageIndex == index
                                ? Colors.blue
                                : Colors.white.withValues(
                                  red: 255,
                                  green: 255,
                                  blue: 255,
                                  alpha: 178,
                                ),
                      ),
                    );
                  }),
                ),
              ),
          ],
        ),
        // Property title, location, price
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          widget.property['listingType'] == 'rent'
                              ? Colors.lightGreen.withValues(
                                red: 139,
                                green: 195,
                                blue: 74,
                                alpha: 255,
                              )
                              : Colors.lightBlue.withValues(
                                red: 3,
                                green: 169,
                                blue: 244,
                                alpha: 255,
                              ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.property['listingType'] == 'rent'
                          ? 'For Rent'
                          : 'For Sale',
                      style: const TextStyle(
                        color: Colors.blueAccent,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  if (hasPanorama) const SizedBox(width: 8),
                  if (hasPanorama)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(
                          red: 255,
                          green: 193,
                          blue: 7,
                          alpha: 255,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.panorama_horizontal,
                            color: Colors.white,
                            size: 15,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            panoramaUrls.length > 1
                                ? '360° Tour (${panoramaUrls.length})'
                                : '360° Tour',
                            style: const TextStyle(
                              color: Colors.blueAccent,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.location_on, color: Colors.grey, size: 16),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      location,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Rating
              Row(
                children: [
                  const Icon(
                    Icons.chat_bubble_outline,
                    color: Colors.grey,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$inquiries inquiries',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
              // Property specifications
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildSpecItem(Icons.king_bed, '$bedrooms', 'Bedrooms'),
                  _buildSpecItem(Icons.bathtub, '$bathrooms', 'Bathrooms'),
                  _buildSpecItem(
                    Icons.square_foot,
                    '${area.toStringAsFixed(0)} sq ft',
                    'Area',
                  ),
                ],
              ),
              const Divider(height: 32),
              // Description section
              const Text(
                'Description',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyle(color: Colors.grey[700], height: 1.5),
              ),
              const Divider(height: 32),
              // Agent information
              const Text(
                'Listed By',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.grey[300],
                    child: const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          sellerName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (sellerEmail.isNotEmpty)
                          Text(
                            sellerEmail,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                      ],
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      final chatService = ChatService();
                      final String propertyId = widget.property['id'] ?? '';
                      final String agentId = widget.property['userId'] ?? '';
                      final String propertyTitle =
                          widget.property['title'] ?? 'Property';
                      final String agentName =
                          widget.property['userName'] ?? 'Agent';

                      if (propertyId.isEmpty || agentId.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Unable to connect with agent'),
                          ),
                        );
                        return;
                      }

                      final conversationId = await chatService
                          .startConversation(
                            propertyId,
                            agentId,
                            propertyTitle,
                            agentName,
                          );

                      if (conversationId.isNotEmpty && context.mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ChatDetailScreen(
                                  conversationId: conversationId,
                                  agentId: agentId,
                                  agentName: agentName,
                                  propertyId: propertyId,
                                  propertyTitle: propertyTitle,
                                ),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.lightBlueAccent,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Chat'),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // Property type, listing type, and availability tags
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildTag(propertyType, Colors.grey[200]!),
                  _buildTag(listingType, Colors.grey[200]!),
                  if (isAvailable)
                    _buildTag(
                      'Available',
                      Colors.green[100]!,
                      textColor: Colors.green[700],
                    ),
                ],
              ),
              const SizedBox(height: 24),
              // Price and call to action
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(
                    red: 33,
                    green: 150,
                    blue: 243,
                    alpha: 0.1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Price',
                              style: TextStyle(
                                color: Colors.grey[700],
                                fontSize: 14,
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  '₱${price.toStringAsFixed(2)}${widget.property['listingType'] == 'rent' ? '/month' : ''}',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Listed ${_getTimeAgo(listedDate)}',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
      ],
    );
  }

  Widget _buildTag(String text, Color backgroundColor, {Color? textColor}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: TextStyle(color: textColor ?? Colors.black87, fontSize: 14),
      ),
    );
  }

  String _getTimeAgo(DateTime date) {
    final difference = DateTime.now().difference(date);
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inMinutes} minutes ago';
    }
  }
}
