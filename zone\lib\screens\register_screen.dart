import 'package:flutter/material.dart';
import '../widgets/reusable_widget.dart';
import 'dart:math' as math;
import '../firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:firebase_auth/firebase_auth.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  String? selectedRole;
  bool termsAgreed = false;
  final List<String> roles = ['Tenant', 'Landlord', 'Buyer', 'Seller'];
  bool _isLoading = false;
  String? _errorMessage;
  final Auth _auth = Auth();

  // Controllers for form fields
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _contactController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Validate form fields
  bool _validateForm() {
    if (_firstNameController.text.trim().isEmpty ||
        _lastNameController.text.trim().isEmpty ||
        _emailController.text.trim().isEmpty ||
        _addressController.text.trim().isEmpty ||
        _passwordController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Please fill all required fields';
      });
      return false;
    }

    if (!termsAgreed) {
      setState(() {
        _errorMessage = 'Please agree to the Terms & Conditions';
      });
      return false;
    }

    return true;
  }

  // Register a new user
  Future<void> _register() async {
    // Clear previous error
    setState(() {
      _errorMessage = null;
    });

    // Check for terms agreement first
    if (!termsAgreed) {
      setState(() {
        _errorMessage = 'Please agree to the Terms & Conditions';
      });
      return;
    }

    // Validate form
    if (!_validateForm()) {
      return;
    }

    // Check if role is selected
    if (selectedRole == null) {
      setState(() {
        _errorMessage = 'Please select a role';
      });
      return;
    }

    // Start loading
    setState(() {
      _isLoading = true;
    });

    try {
      // Create user with Firebase Auth and save additional info
      await _auth.createUserWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        role: selectedRole,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        address: _addressController.text.trim(),
        contact: _contactController.text.trim(),
        username: _usernameController.text.trim(),
      );

      // Send verification email to current user
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        user.sendEmailVerification();
      }

      if (mounted) {
        // Show verification dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Verify Your Email'),
              content: const Text(
                'A verification link has been sent to your email address. Please verify your email before logging in.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      // Show error message with improved formatting
      if (mounted) {
        setState(() {
          String errorMsg = e.toString();

          // Remove 'firebase_auth/' prefix if it exists
          if (errorMsg.contains('firebase_auth/')) {
            errorMsg = errorMsg.replaceAll('firebase_auth/', '');
          }

          _errorMessage = 'Registration failed: $errorMsg';
        });
      }
    } finally {
      // End loading
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Show terms and conditions dialog
  void _showTermsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Terms & Conditions',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Text(
              'Welcome to Property Edge!\n\n'
              '1. ACCEPTANCE OF TERMS\n'
              'By using our app, you agree to these Terms & Conditions.\n\n'
              '2. DESCRIPTION OF SERVICE\n'
              'Property Edge provides a platform for users to buy, sell, rent, or lease properties.\n\n'
              '3. USER ACCOUNTS\n'
              'You are responsible for maintaining the confidentiality of your account credentials.\n\n'
              '4. USER CONDUCT\n'
              'You agree not to use our service to upload illegal content or engage in harmful activities.\n\n'
              '5. PROPERTY LISTINGS\n'
              'All property listings must be accurate and comply with local laws and regulations.\n\n'
              '6. PRIVACY\n'
              'Your privacy is important to us. Please review our Privacy Policy for information on how we collect and use your data.\n\n'
              '7. TERMINATION\n'
              'We reserve the right to terminate accounts that violate our terms.\n\n'
              '8. LIMITATION OF LIABILITY\n'
              'Property Edge is not liable for any damages arising from your use of our services.\n\n'
              '9. CHANGES TO TERMS\n'
              'We may update these terms at any time. Continued use of our service constitutes acceptance of updated terms.\n\n'
              '10. CONTACT US\n'
              'For questions or concerns about these terms, <NAME_EMAIL>',
              style: TextStyle(fontSize: 14),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Show privacy policy dialog
  void _showPrivacyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Privacy Policy',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Text(
              'PRIVACY POLICY\n\n'
              'Last updated: ${DateTime.now().year}\n\n'
              '1. INFORMATION WE COLLECT\n'
              'We collect personal information including name, email, phone number, and location data when you use our app.\n\n'
              '2. HOW WE USE YOUR INFORMATION\n'
              '• To provide and maintain our service\n'
              '• To notify you about changes to our service\n'
              '• To allow you to participate in interactive features\n'
              '• To provide customer support\n'
              '• To gather analysis or valuable information to improve our service\n'
              '• To monitor the usage of our service\n'
              '• To detect, prevent and address technical issues\n\n'
              '3. DATA SECURITY\n'
              'The security of your data is important to us, but remember that no method of transmission over the Internet is 100% secure.\n\n'
              '4. DATA RETENTION\n'
              'We will retain your personal data only for as long as necessary for the purposes set out in this Privacy Policy.\n\n'
              '5. YOUR DATA PROTECTION RIGHTS\n'
              'You have the right to access, update, or delete your information at any time.\n\n'
              '6. CHANGES TO THIS PRIVACY POLICY\n'
              'We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.\n\n'
              '7. CONTACT US\n'
              'If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>',
              style: TextStyle(fontSize: 14),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    final horizontalPadding = size.width * 0.06;

    return Scaffold(
      backgroundColor: const Color.fromRGBO(
        240,
        248,
        255,
        0.9,
      ), // Azure tint with transparency
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: size.height * 0.03,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with logo and title
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Register',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 26 : 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[600],
                      ),
                    ),
                    Text(
                      'Property Edge',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 16 : 18,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.02),

                // Divider
                Divider(color: Colors.grey[300], thickness: 1),
                SizedBox(height: size.height * 0.02),

                // Form with light blue transparent background
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(
                      red: 0,
                      green: 140,
                      blue: 255,
                      alpha: 0.05,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withValues(
                        red: 0,
                        green: 140,
                        blue: 255,
                        alpha: 0.1,
                      ),
                    ),
                  ),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Determine if we should use a single column layout
                      final useSingleColumn = constraints.maxWidth < 500;

                      return Column(
                        children: [
                          // Name Fields
                          if (useSingleColumn)
                            // Single column layout for small screens
                            Column(
                              children: [
                                CustomTextFormField(
                                  labelText: 'First Name',
                                  controller: _firstNameController,
                                ),
                                SizedBox(height: size.height * 0.015),
                                CustomTextFormField(
                                  labelText: 'Last Name',
                                  controller: _lastNameController,
                                ),
                              ],
                            )
                          else
                            // Two column layout for larger screens
                            Row(
                              children: [
                                Expanded(
                                  child: CustomTextFormField(
                                    labelText: 'First Name',
                                    controller: _firstNameController,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: CustomTextFormField(
                                    labelText: 'Last Name',
                                    controller: _lastNameController,
                                  ),
                                ),
                              ],
                            ),
                          SizedBox(height: size.height * 0.015),

                          // Email field
                          CustomTextFormField(
                            labelText: 'Email',
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                          ),
                          SizedBox(height: size.height * 0.015),

                          // Address field
                          CustomTextFormField(
                            labelText: 'Address',
                            controller: _addressController,
                          ),
                          SizedBox(height: size.height * 0.015),

                          // Role and Contact Number
                          if (useSingleColumn)
                            Column(
                              children: [
                                CustomDropdown(
                                  value: selectedRole,
                                  hint: 'Tenant',
                                  items: roles,
                                  onChanged: (String? value) {
                                    setState(() {
                                      selectedRole = value;
                                    });
                                  },
                                ),
                                SizedBox(height: size.height * 0.015),
                                CustomTextFormField(
                                  labelText: 'Contact Number',
                                  controller: _contactController,
                                  keyboardType: TextInputType.phone,
                                ),
                              ],
                            )
                          else
                            Row(
                              children: [
                                Expanded(
                                  child: CustomDropdown(
                                    value: selectedRole,
                                    hint: 'Tenant',
                                    items: roles,
                                    onChanged: (String? value) {
                                      setState(() {
                                        selectedRole = value;
                                      });
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: CustomTextFormField(
                                    labelText: 'Contact Number',
                                    controller: _contactController,
                                    keyboardType: TextInputType.phone,
                                  ),
                                ),
                              ],
                            ),
                          SizedBox(height: size.height * 0.015),

                          // Username and Password
                          if (useSingleColumn)
                            Column(
                              children: [
                                CustomTextFormField(
                                  labelText: 'Username',
                                  controller: _usernameController,
                                ),
                                SizedBox(height: size.height * 0.015),
                                CustomTextFormField(
                                  labelText: 'Password',
                                  controller: _passwordController,
                                  obscureText: true,
                                ),
                              ],
                            )
                          else
                            Row(
                              children: [
                                Expanded(
                                  child: CustomTextFormField(
                                    labelText: 'Username',
                                    controller: _usernameController,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: CustomTextFormField(
                                    labelText: 'Password',
                                    controller: _passwordController,
                                    obscureText: true,
                                  ),
                                ),
                              ],
                            ),
                          SizedBox(height: size.height * 0.02),

                          // Terms & Agreement Checkbox
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: Checkbox(
                                      value: termsAgreed,
                                      activeColor: Colors.blue[600],
                                      side: BorderSide(
                                        color:
                                            _errorMessage != null &&
                                                    !termsAgreed
                                                ? Colors.red
                                                : Colors.grey,
                                        width:
                                            _errorMessage != null &&
                                                    !termsAgreed
                                                ? 2
                                                : 1,
                                      ),
                                      onChanged: (bool? value) {
                                        setState(() {
                                          termsAgreed = value ?? false;
                                          // Clear error message if user checks the box
                                          if (termsAgreed &&
                                              _errorMessage ==
                                                  'Please agree to the Terms & Conditions') {
                                            _errorMessage = null;
                                          }
                                        });
                                      },
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          color:
                                              _errorMessage != null &&
                                                      !termsAgreed
                                                  ? Colors.red
                                                  : Colors.grey[700],
                                          fontSize: isSmallScreen ? 14 : 16,
                                        ),
                                        children: [
                                          const TextSpan(
                                            text: 'I agree to the ',
                                          ),
                                          TextSpan(
                                            text: 'Terms & Conditions',
                                            style: TextStyle(
                                              color:
                                                  _errorMessage != null &&
                                                          !termsAgreed
                                                      ? Colors.red
                                                      : Colors.blue[600],
                                              fontWeight: FontWeight.bold,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            recognizer:
                                                TapGestureRecognizer()
                                                  ..onTap = () {
                                                    // Show terms dialog
                                                    _showTermsDialog(context);
                                                  },
                                          ),
                                          const TextSpan(text: ' and '),
                                          TextSpan(
                                            text: 'Privacy Policy',
                                            style: TextStyle(
                                              color:
                                                  _errorMessage != null &&
                                                          !termsAgreed
                                                      ? Colors.red
                                                      : Colors.blue[600],
                                              fontWeight: FontWeight.bold,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            recognizer:
                                                TapGestureRecognizer()
                                                  ..onTap = () {
                                                    // Show privacy policy dialog
                                                    _showPrivacyDialog(context);
                                                  },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              // Display terms error message specifically here
                              if (_errorMessage ==
                                  'Please agree to the Terms & Conditions')
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: 4.0,
                                    left: 32.0,
                                  ),
                                  child: Text(
                                    '* You must accept terms to continue',
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),

                          // Show error message if exists
                          if (_errorMessage != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 12.0),
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ),

                SizedBox(height: size.height * 0.03),

                // Register Button
                Center(
                  child:
                      _isLoading
                          ? const CircularProgressIndicator()
                          : CustomButton(
                            text: 'Register',
                            onPressed: _register,
                            borderRadius: 100.0,
                            backgroundColor: Colors.blue[600]!,
                            width: math.min(size.width * 0.85, 400),
                          ),
                ),
                SizedBox(height: size.height * 0.02),

                // Already have an account
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Already have an account? ',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.pushReplacementNamed(context, '/login');
                        },
                        child: Text(
                          'Login',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 14 : 16,
                            color: Colors.blue[600],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
