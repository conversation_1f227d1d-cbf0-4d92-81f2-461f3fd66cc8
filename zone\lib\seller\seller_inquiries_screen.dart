import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import '../screens/chat_detail_screen.dart';

class SellerInquiriesScreen extends StatefulWidget {
  const SellerInquiriesScreen({super.key});

  @override
  State<SellerInquiriesScreen> createState() => _SellerInquiriesScreenState();
}

class _SellerInquiriesScreenState extends State<SellerInquiriesScreen> {
  final Logger _logger = Logger();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  bool _isLoading = true;
  List<DocumentSnapshot> _conversations = [];

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Query conversations where the current user is a participant (as an agent)
      final QuerySnapshot snapshot =
          await _firestore
              .collection('conversations')
              .where('participants', arrayContains: userId)
              .orderBy('lastMessageTime', descending: true)
              .get();

      setState(() {
        _conversations = snapshot.docs;
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('Error loading conversations: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Property Inquiries')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _conversations.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No inquiries yet',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Messages from interested buyers will appear here',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              )
              : RefreshIndicator(
                onRefresh: _loadConversations,
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: _conversations.length,
                  itemBuilder: (context, index) {
                    final conversation =
                        _conversations[index].data() as Map<String, dynamic>;

                    // Extract conversation data
                    final String conversationId = _conversations[index].id;
                    final String propertyTitle =
                        conversation['propertyTitle'] ?? 'Property';
                    final String lastMessage =
                        conversation['lastMessage'] ?? '';
                    final Timestamp lastMessageTime =
                        conversation['lastMessageTime'] as Timestamp? ??
                        Timestamp.now();
                    final int unreadCount =
                        conversation['unreadCount'] as int? ?? 0;

                    final String buyerName =
                        conversation['buyerName'] ?? 'Interested Buyer';
                    final String agentId = conversation['agentId'] ?? '';
                    final String propertyId = conversation['propertyId'] ?? '';

                    // Format relative time for display
                    final now = DateTime.now();
                    final messageTime = lastMessageTime.toDate();
                    final difference = now.difference(messageTime);

                    String timeText;
                    if (difference.inDays > 7) {
                      // Format as MM/DD/YYYY
                      timeText =
                          '${messageTime.month}/${messageTime.day}/${messageTime.year}';
                    } else if (difference.inDays > 0) {
                      timeText = '${difference.inDays}d ago';
                    } else if (difference.inHours > 0) {
                      timeText = '${difference.inHours}h ago';
                    } else if (difference.inMinutes > 0) {
                      timeText = '${difference.inMinutes}m ago';
                    } else {
                      timeText = 'Just now';
                    }

                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      leading: CircleAvatar(
                        backgroundColor:
                            unreadCount > 0 ? Colors.blue : Colors.grey[300],
                        child: Text(
                          buyerName.isNotEmpty
                              ? buyerName[0].toUpperCase()
                              : '?',
                          style: TextStyle(
                            color:
                                unreadCount > 0
                                    ? Colors.white
                                    : Colors.grey[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              buyerName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            timeText,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                            'Re: $propertyTitle',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            lastMessage,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color:
                                  unreadCount > 0
                                      ? Colors.black87
                                      : Colors.grey[600],
                              fontWeight:
                                  unreadCount > 0
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                      trailing:
                          unreadCount > 0
                              ? Container(
                                padding: const EdgeInsets.all(6),
                                decoration: const BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                ),
                                child: Text(
                                  unreadCount.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              )
                              : null,
                      onTap: () {
                        // Navigate to chat detail screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ChatDetailScreen(
                                  conversationId: conversationId,
                                  agentId: agentId,
                                  agentName: buyerName,
                                  propertyId: propertyId,
                                  propertyTitle: propertyTitle,
                                ),
                          ),
                        ).then((_) => _loadConversations());
                      },
                    );
                  },
                ),
              ),
    );
  }
}
