class PropertyFAQ {
  static Map<String, List<String>> getKeywords() {
    return {
      'bedrooms': ['bedroom', 'bed', 'rooms', 'sleep'],
      'bathrooms': ['bathroom', 'bath', 'toilet', 'shower'],
      'price': ['price', 'cost', 'expensive', 'cheap', 'afford'],
      'location': ['location', 'where', 'address', 'area', 'neighborhood'],
      'amenities': [
        'amenities',
        'features',
        'include',
        'facility',
        'pool',
        'gym',
      ],
      'parking': ['parking', 'car', 'garage', 'park'],
      'pets': ['pet', 'dog', 'cat', 'animal', 'allow'],
    };
  }

  static String getResponse(String category, Map<String, dynamic> property) {
    switch (category) {
      case 'bedrooms':
        return 'This property has ${property['bedrooms'] ?? 'N/A'} bedrooms.';
      case 'bathrooms':
        return 'This property has ${property['bathrooms'] ?? 'N/A'} bathrooms.';
      case 'price':
        return 'The price is ₱${property['price']?.toStringAsFixed(2) ?? 'N/A'}${property['listingType'] == 'rent' ? ' per month' : ''}.';
      case 'location':
        return 'This property is located at ${property['location'] ?? 'N/A'}.';
      case 'amenities':
        final List<dynamic> amenities = property['amenities'] ?? [];
        return amenities.isEmpty
            ? 'No specific amenities are listed for this property.'
            : 'This property includes: ${amenities.join(', ')}.';
      case 'parking':
        return property['hasParking'] == true
            ? 'Yes, this property has parking available.'
            : 'No, this property does not have dedicated parking.';
      case 'pets':
        return property['petsAllowed'] == true
            ? 'Yes, pets are allowed in this property.'
            : 'No, pets are not allowed in this property.';
      default:
        return "I don't have specific information about that. Please contact the property owner for more details.";
    }
  }
}
