import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/firestore_collections.dart';
import 'package:logger/logger.dart';
import './admin_users.dart';
import './admin_listings.dart';
import './admin_verifications.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();
  bool _isSidebarCollapsed = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isDarkMode = true;

  final List<Widget> _pages = [
    const AdminDashboardHome(),
    const AdminUsers(),
    const AdminListings(),
    const AdminVerifications(),
  ];

  final List<Map<String, dynamic>> _navItems = [
    {'icon': Icons.dashboard, 'label': 'Dashboard'},
    {'icon': Icons.people, 'label': 'Users'},
    {'icon': Icons.home, 'label': 'Listings'},
    {'icon': Icons.verified, 'label': 'Verifications'},
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme =
        _isDarkMode
            ? const ColorScheme.dark(
              primary: Color.fromARGB(
                255,
                17,
                140,
                247,
              ), // Forest green - real estate color
              secondary: Color(0xFF546E7A), // Blue-grey
              surface: Color(0xFF121212),
              surfaceContainer: Color(0xFF121212),
              onSurface: Colors.white,
              tertiary: Color(0xFF8D6E63), // Brown - wood/earth tone
            )
            : ColorScheme.light(
              primary: const Color.fromRGBO(
                75,
                165,
                238,
                1,
              ), // Forest green - real estate color
              secondary: const Color(0xFF546E7A), // Blue-grey
              surface: const Color(0xFFF5F9F6), // Light sage green background
              surfaceContainer: const Color(
                0xFFF5F9F6,
              ), // Light sage green background
              onSurface: Colors.black,
              tertiary: const Color(0xFF8D6E63), // Brown - wood/earth tone
            );

    return Theme(
      data: ThemeData(
        colorScheme: colorScheme,
        scaffoldBackgroundColor: colorScheme.surfaceContainer,
        appBarTheme: AppBarTheme(
          backgroundColor: _isDarkMode ? colorScheme.surface : Colors.white,
          foregroundColor: colorScheme.onSurface,
          elevation: 0,
        ),
        cardTheme: CardThemeData(
          color: colorScheme.surface,
          shadowColor: Colors.black26,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        floatingActionButtonTheme: FloatingActionButtonThemeData(
          backgroundColor: colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ),
      child: Scaffold(
        key: _scaffoldKey,
        drawer:
            MediaQuery.of(context).size.width < 1200
                ? _buildSidebar(colorScheme)
                : null,
        body: Row(
          children: [
            if (MediaQuery.of(context).size.width >= 1200)
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: _isSidebarCollapsed ? 70 : 250,
                child: _buildSidebar(colorScheme),
              ),
            Expanded(
              child: Column(
                children: [
                  _buildAppBar(colorScheme),
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: _pages,
                      onPageChanged: (index) {
                        setState(() {
                          _selectedIndex = index;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(ColorScheme colorScheme) {
    return Container(
      height: 64,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(
              red: 0,
              green: 0,
              blue: 0,
              alpha: 0.05,
            ),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          if (MediaQuery.of(context).size.width < 1200)
            IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () {
                _scaffoldKey.currentState?.openDrawer();
              },
            ),
          const Spacer(),
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: _toggleTheme,
          ),
          const SizedBox(width: 8),
          StreamBuilder<QuerySnapshot>(
            stream:
                FirebaseFirestore.instance
                    .collection('notifications')
                    .where('read', isEqualTo: false)
                    .snapshots(),
            builder: (context, snapshot) {
              int count = 0;
              if (snapshot.hasData) {
                count = snapshot.data!.docs.length;
              }

              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications_none),
                    onPressed: () {
                      // Navigate to AdminVerificationsScreen
                      setState(() {
                        _selectedIndex = 3;
                        _pageController.jumpToPage(3);
                      });
                    },
                  ),
                  if (count > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: const Color(
                            0xFF8D6E63,
                          ), // Brown notification badge
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 18,
                          minHeight: 18,
                        ),
                        child: Text(
                          count > 9 ? '9+' : count.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          const SizedBox(width: 16),
          CircleAvatar(
            backgroundColor: colorScheme.primary,
            radius: 18,
            child: const Icon(Icons.person, color: Colors.white),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  Widget _buildSidebar(ColorScheme colorScheme) {
    return Container(
      color:
          _isDarkMode
              ? colorScheme.surface
              : const Color(0xFFEBF5ED), // Light green background
      child: Column(
        children: [
          Container(
            height: 64,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerLeft,
            color: colorScheme.primary, // Green header
            child: Row(
              children: [
                const Text(
                  'Property Edge',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                if (MediaQuery.of(context).size.width >= 1200)
                  IconButton(
                    icon: Icon(
                      _isSidebarCollapsed
                          ? Icons.arrow_forward_ios
                          : Icons.arrow_back_ios,
                      size: 16,
                    ),
                    onPressed: () {
                      setState(() {
                        _isSidebarCollapsed = !_isSidebarCollapsed;
                      });
                    },
                  ),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: ListView.builder(
              itemCount: _navItems.length,
              itemBuilder: (context, index) {
                final item = _navItems[index];
                final isSelected = _selectedIndex == index;
                return ListTile(
                  leading: Icon(
                    item['icon'],
                    color:
                        isSelected
                            ? colorScheme.primary
                            : colorScheme.onSurface,
                  ),
                  title:
                      _isSidebarCollapsed
                          ? null
                          : Text(
                            item['label'],
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? colorScheme.primary
                                      : colorScheme.onSurface,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                  selected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                      _pageController.jumpToPage(index);
                    });
                    if (MediaQuery.of(context).size.width < 1200) {
                      Navigator.pop(context);
                    }
                  },
                );
              },
            ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.logout),
            title: _isSidebarCollapsed ? null : const Text('Logout'),
            onTap: () async {
              await FirebaseAuth.instance.signOut();
              if (!mounted) return;
              Navigator.of(context).pushReplacementNamed('/login');
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class AdminDashboardHome extends StatefulWidget {
  const AdminDashboardHome({super.key});

  @override
  State<AdminDashboardHome> createState() => _AdminDashboardHomeState();
}

class _AdminDashboardHomeState extends State<AdminDashboardHome>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final Logger _logger = Logger();
  Map<String, dynamic> _stats = {
    'totalUsers': 0,
    'totalSellers': 0,
    'totalLandlords': 0,
    'totalBuyers': 0,
    'totalTenants': 0,
    'totalListings': 0,
    'activeListings': 0,
    'rentedProperties': 0,
  };
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get total users count
      final usersCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .count()
              .get();

      // Get users by role
      final sellersCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .where('role', isEqualTo: 'Seller')
              .count()
              .get();

      final landlordsCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .where('role', isEqualTo: 'Landlord')
              .count()
              .get();

      final buyersCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .where('role', isEqualTo: 'Buyer')
              .count()
              .get();

      final tenantsCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .where('role', isEqualTo: 'Tenant')
              .count()
              .get();

      // Get property listings stats
      final listingsCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .count()
              .get();

      final activeListingsCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('status', isEqualTo: 'available')
              .count()
              .get();

      final rentedPropertiesCount =
          await FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('status', isEqualTo: 'rented')
              .count()
              .get();

      // Update state
      if (mounted) {
        setState(() {
          _stats = {
            'totalUsers': usersCount.count,
            'totalSellers': sellersCount.count,
            'totalLandlords': landlordsCount.count,
            'totalBuyers': buyersCount.count,
            'totalTenants': tenantsCount.count,
            'totalListings': listingsCount.count,
            'activeListings': activeListingsCount.count,
            'rentedProperties': rentedPropertiesCount.count,
          };
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('Error loading stats: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Dashboard',
                      style: textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 24),
                    _buildStatsGrid(),
                    const SizedBox(height: 32),
                    _buildRecentActivity(),
                  ],
                ),
              ),
    );
  }

  Widget _buildStatsGrid() {
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = screenWidth > 600 ? 2 : 1;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      childAspectRatio: screenWidth > 600 ? 1.8 : 1.5,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildStatCard(
          title: 'Active Listings',
          value: _stats['activeListings'].toString(),
          subtitle:
              '${_stats['rentedProperties']} for rent, ${_stats['totalListings'] - _stats['rentedProperties']} for sale',
          icon: Icons.description_outlined,
          color: Colors.blue[600]!,
        ),
        _buildStatCard(
          title: 'New Inquiries',
          value: _stats['totalUsers'].toString(),
          subtitle:
              '${_stats['totalSellers']} sellers, ${_stats['totalLandlords']} landlords, ${_stats['totalBuyers']} buyers, ${_stats['totalTenants']} tenants',
          icon: Icons.chat_bubble_outline,
          color: Colors.green[600]!,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Theme.of(context).cardColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(
                      color.r.toInt(),
                      color.g.toInt(),
                      color.b.toInt(),
                      0.1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activity',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Users'),
                  Tab(text: 'Listings'),
                  Tab(text: 'Pending Verifications'),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 400,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildRecentUsersTab(),
                    _buildRecentListingsTab(),
                    _buildPendingVerificationsTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecentUsersTab() {
    return FutureBuilder<QuerySnapshot>(
      future:
          FirebaseFirestore.instance
              .collection(FirestoreCollections.users)
              .orderBy('createdAt', descending: true)
              .limit(5)
              .get(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final users = snapshot.data?.docs ?? [];

        if (users.isEmpty) {
          return const Center(child: Text('No recent users'));
        }

        return ListView.separated(
          itemCount: users.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final user = users[index].data() as Map<String, dynamic>;
            final String firstName = user['firstName'] ?? '';
            final String lastName = user['lastName'] ?? '';
            final String fullName = '$firstName $lastName'.trim();
            final String email = user['email'] ?? 'No email';
            final String role = user['role'] ?? 'Unknown';

            final createdAt =
                user['createdAt'] is Timestamp
                    ? (user['createdAt'] as Timestamp).toDate()
                    : DateTime.now();

            return ListTile(
              leading: CircleAvatar(
                child: Text(fullName.isNotEmpty ? fullName[0] : 'U'),
              ),
              title: Text(fullName.isNotEmpty ? fullName : 'Unknown User'),
              subtitle: Text('$email • $role'),
              trailing: Text(
                _formatDate(createdAt),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              onTap: () {
                // Navigate to user details
              },
            );
          },
        );
      },
    );
  }

  Widget _buildRecentListingsTab() {
    return FutureBuilder<QuerySnapshot>(
      future:
          FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .orderBy('createdAt', descending: true)
              .limit(5)
              .get(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final listings = snapshot.data?.docs ?? [];

        if (listings.isEmpty) {
          return const Center(child: Text('No recent listings'));
        }

        return ListView.separated(
          itemCount: listings.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final listing = listings[index].data() as Map<String, dynamic>;
            final String title = listing['title'] ?? 'Untitled';
            final String location = listing['location'] ?? 'No location';
            final double price =
                listing['price'] is double
                    ? listing['price']
                    : listing['price'] is int
                    ? (listing['price'] as int).toDouble()
                    : 0.0;
            final String listingType = listing['listingType'] ?? 'sale';

            String priceDisplay;
            if (listingType == 'rent') {
              priceDisplay = '\$${price.toStringAsFixed(0)}/mo';
            } else {
              priceDisplay = '\$${price.toStringAsFixed(0)}';
            }

            final createdAt =
                listing['createdAt'] is int
                    ? DateTime.fromMillisecondsSinceEpoch(
                      listing['createdAt'] as int,
                    )
                    : DateTime.now();

            return ListTile(
              leading: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(
                    red: 0,
                    green: 122,
                    blue: 255,
                    alpha: 0.2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(child: Icon(Icons.home, color: Colors.blue)),
              ),
              title: Text(title),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(location),
                  Text(
                    _formatTimeAgo(createdAt),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.secondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    priceDisplay,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(
                        red: 255,
                        green: 165,
                        blue: 0,
                        alpha: 0.2,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      listingType == 'rent' ? 'For Rent' : 'For Sale',
                      style: TextStyle(
                        fontSize: 12,
                        color:
                            listingType == 'rent' ? Colors.purple : Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              onTap: () {
                // Navigate to listing details
              },
            );
          },
        );
      },
    );
  }

  Widget _buildPendingVerificationsTab() {
    return FutureBuilder<QuerySnapshot>(
      future:
          FirebaseFirestore.instance
              .collection(FirestoreCollections.properties)
              .where('isApproved', isEqualTo: false)
              .limit(5)
              .get(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final properties = snapshot.data?.docs ?? [];

        if (properties.isEmpty) {
          return const Center(child: Text('No pending verifications'));
        }

        return ListView.builder(
          itemCount: properties.length,
          itemBuilder: (context, index) {
            final property = properties[index].data() as Map<String, dynamic>;
            final String title = property['title'] ?? 'Untitled Property';
            final String type = property['propertyType'] ?? 'Unknown';

            final createdAt =
                property['createdAt'] is Timestamp
                    ? (property['createdAt'] as Timestamp).toDate()
                    : DateTime.now();

            return Card(
              margin: const EdgeInsets.all(8),
              child: ListTile(
                leading: const Icon(Icons.home),
                title: Text(title),
                subtitle: Text(
                  '$type • Submitted ${_formatTimeAgo(createdAt)}',
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextButton(
                      child: const Text('Decline'),
                      onPressed: () {
                        // Implementation for declining property
                      },
                    ),
                    TextButton(
                      child: const Text('Approve'),
                      onPressed: () {
                        // Implementation for approving property
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 7) {
      return _formatDate(dateTime);
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.month}/${dateTime.day}/${dateTime.year}';
  }
}
