import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import '../firebase_auth.dart';
import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ProfileCustomerScreen extends StatefulWidget {
  const ProfileCustomerScreen({super.key});

  @override
  State<ProfileCustomerScreen> createState() => _ProfileCustomerScreenState();
}

class _ProfileCustomerScreenState extends State<ProfileCustomerScreen> {
  int _selectedIndex = 3; // Profile tab is selected
  final Auth _auth = Auth();
  Map<String, dynamic>? _userData;
  bool _isLoading = false;
  String? _profileImageBase64;
  final Logger _logger = Logger();
  final ImagePicker _picker = ImagePicker();

  // Stats counters
  int _viewedCount = 0;
  int _savedCount = 0;
  int _inquiriesCount = 0;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadUserStats();
  }

  Future<void> _loadUserData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userData = await _auth.getUserData();
      if (!mounted) return;

      setState(() {
        _userData = userData;
        // If profile image exists in userData, use it
        if (userData != null &&
            userData['profileImage'] != null &&
            userData['profileImage'].isNotEmpty) {
          _profileImageBase64 = userData['profileImage'];
        }
      });
    } catch (e) {
      // Handle error
      _logger.e('Error loading user data: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Function to load user statistics
  Future<void> _loadUserStats() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    try {
      // Get saved/favorites count
      final favoritesQuery =
          await FirebaseFirestore.instance
              .collection('favorites')
              .where('userId', isEqualTo: userId)
              .get();

      // Get inquiries/messages count
      final conversationsQuery =
          await FirebaseFirestore.instance
              .collection('conversations')
              .where('participants', arrayContains: userId)
              .get();

      // Get viewed properties count (assuming there's a 'views' collection)
      final viewsQuery =
          await FirebaseFirestore.instance
              .collection('views')
              .where('userId', isEqualTo: userId)
              .get();

      if (mounted) {
        setState(() {
          _savedCount = favoritesQuery.docs.length;
          _inquiriesCount = conversationsQuery.docs.length;
          _viewedCount = viewsQuery.docs.length;
        });
      }
    } catch (e) {
      _logger.e('Error loading user stats: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header
            _buildProfileHeader(),

            // Stats
            _buildStatsRow(),

            const SizedBox(height: 16),

            // Contact Information Card
            _buildContactInfoCard(),

            const SizedBox(height: 16),

            // Settings Section
            _buildSettingsSection(),

            const SizedBox(height: 24),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  // Clean blue banner with profile header
  Widget _buildProfileHeader() {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        // Blue header
        Container(
          width: double.infinity,
          height: 110,
          color: Colors.blue,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: SafeArea(
            bottom: false,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  'Profile',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Profile picture with tap to change functionality
        Positioned(
          top: 90,
          child: Center(
            child: Column(
              children: [
                Stack(
                  children: [
                    _isLoading
                        ? Container(
                          width: 100,
                          height: 100,
                          decoration: const BoxDecoration(
                            color: Colors.grey,
                            shape: BoxShape.circle,
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                          ),
                        )
                        : CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.grey,
                          backgroundImage:
                              _profileImageBase64 != null &&
                                      _profileImageBase64!.isNotEmpty
                                  ? _getProfileImageProvider(
                                    _profileImageBase64!,
                                  )
                                  : null,
                          child:
                              _profileImageBase64 == null ||
                                      _profileImageBase64!.isEmpty
                                  ? const Icon(
                                    Icons.person,
                                    size: 60,
                                    color: Colors.white,
                                  )
                                  : null,
                        ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // User name, status, and edit button
  Widget _buildUserInfoSection() {
    final String displayName =
        _userData != null
            ? '${_userData!['firstName']} ${_userData!['lastName']}'
            : 'Loading...';

    return Column(
      children: [
        const SizedBox(height: 90),
        Container(
          alignment: Alignment.center,
          width: double.infinity,
          child: Text(
            displayName,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 20),
        OutlinedButton(
          onPressed: _editProfile,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.black,
            side: const BorderSide(color: Colors.grey),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
          ),
          child: const Text('Edit Profile'),
        ),
      ],
    );
  }

  // Function to handle edit profile
  void _editProfile() {
    // Create controllers and initialize with current user data
    final TextEditingController firstNameController = TextEditingController(
      text: _userData != null ? _userData!['firstName'] : '',
    );
    final TextEditingController lastNameController = TextEditingController(
      text: _userData != null ? _userData!['lastName'] : '',
    );
    final TextEditingController addressController = TextEditingController(
      text: _userData != null ? _userData!['address'] : '',
    );
    final TextEditingController contactController = TextEditingController(
      text: _userData != null ? _userData!['contact'] : '',
    );
    final TextEditingController usernameController = TextEditingController(
      text: _userData != null ? _userData!['username'] : '',
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Edit Profile'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Change photo button
                TextButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    _pickImage();
                  },
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Change Photo'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
                const Divider(),
                TextField(
                  controller: firstNameController,
                  decoration: const InputDecoration(labelText: 'First Name'),
                ),
                TextField(
                  controller: lastNameController,
                  decoration: const InputDecoration(labelText: 'Last Name'),
                ),
                TextField(
                  controller: addressController,
                  decoration: const InputDecoration(labelText: 'Address'),
                ),
                TextField(
                  controller: contactController,
                  decoration: const InputDecoration(
                    labelText: 'Contact Number',
                  ),
                  keyboardType: TextInputType.phone,
                ),
                TextField(
                  controller: usernameController,
                  decoration: const InputDecoration(labelText: 'Username'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Close dialog first
                Navigator.of(context).pop();

                // Then update profile
                _updateUserProfile(
                  firstNameController.text.trim(),
                  lastNameController.text.trim(),
                  addressController.text.trim(),
                  contactController.text.trim(),
                  usernameController.text.trim(),
                );
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  // Image picker method
  Future<void> _pickImage() async {
    _logger.d('Image picker started');
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 70,
      );

      _logger.d('Image selected: ${image != null}');

      if (image != null) {
        setState(() {
          _isLoading = true;
        });

        try {
          final bytes = await image.readAsBytes();
          final base64Image = base64Encode(bytes);
          _logger.d('Image converted to base64, length: ${base64Image.length}');

          // Update UI first
          setState(() {
            _profileImageBase64 = base64Image;
          });

          // Save image to Firestore
          await _auth.updateUserProfile(profileImage: base64Image);

          if (!mounted) return;
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile picture updated successfully'),
            ),
          );
        } catch (e) {
          _logger.e('Error processing image: $e');
          if (!mounted) return;
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating profile picture: $e')),
          );
        }
      }
    } catch (e) {
      _logger.e('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Could not access gallery: $e')));
      }
    }
  }

  // Separate function to update user profile
  Future<void> _updateUserProfile(
    String firstName,
    String lastName,
    String address,
    String contact,
    String username,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _auth.updateUserProfile(
        firstName: firstName,
        lastName: lastName,
        address: address,
        contact: contact,
        username: username,
      );

      // Reload user data to reflect changes
      if (!mounted) return;
      await _loadUserData();

      // Show success message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated successfully')),
      );
    } catch (e) {
      // Show error message
      _logger.e('Error updating profile: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating profile: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Activity stats row with viewed, saved, inquiries
  Widget _buildStatsRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildUserInfoSection(),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('$_viewedCount', 'Viewed'),
              _buildStatItem('$_savedCount', 'Saved'),
              _buildStatItem('$_inquiriesCount', 'Inquiries'),
            ],
          ),
        ],
      ),
    );
  }

  // Individual stat item
  Widget _buildStatItem(String count, String label) {
    return GestureDetector(
      onTap: () {
        // Navigate based on the stat type
        if (label == 'Saved') {
          Navigator.pushReplacementNamed(context, '/favorite');
        } else if (label == 'Inquiries') {
          Navigator.pushReplacementNamed(context, '/messages');
        } else if (label == 'Viewed') {
          // Navigate to viewed properties screen
          Navigator.pushNamed(context, '/viewed_properties');
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              count,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(
              label,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  // Contact information card
  Widget _buildContactInfoCard() {
    final String address =
        _userData != null && _userData!['address'] != null
            ? _userData!['address']
            : 'Not provided';

    final String email =
        _userData != null && _userData!['email'] != null
            ? _userData!['email']
            : 'Not provided';

    final String contact =
        _userData != null && _userData!['contact'] != null
            ? _userData!['contact']
            : 'Not provided';

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Contact Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildContactItem(Icons.location_on, address),
            const SizedBox(height: 12),
            _buildContactItem(Icons.email, email),
            const SizedBox(height: 12),
            _buildContactItem(Icons.phone, contact),
          ],
        ),
      ),
    );
  }

  // Individual contact item
  Widget _buildContactItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, color: Colors.grey, size: 22),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 16),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // Settings section
  Widget _buildSettingsSection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          _buildSettingItem(
            Icons.logout,
            'Logout',
            textColor: Colors.red,
            onTap: () {
              Navigator.pushReplacementNamed(context, '/onboarding');
            },
          ),
        ],
      ),
    );
  }

  // Single setting item
  Widget _buildSettingItem(
    IconData icon,
    String text, {
    Color? textColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap ?? () {},
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            Icon(icon, color: textColor ?? Colors.grey[700], size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 16,
                  color: textColor ?? Colors.black,
                ),
              ),
            ),
            const Icon(Icons.chevron_right, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  // Bottom navigation bar
  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      onTap: (index) {
        if (index != _selectedIndex) {
          setState(() {
            _selectedIndex = index;
          });

          if (index == 0) {
            Navigator.pushReplacementNamed(context, '/home');
          } else if (index == 1) {
            Navigator.pushReplacementNamed(context, '/messages');
          } else if (index == 2) {
            Navigator.pushReplacementNamed(context, '/favorite');
          }
          // Index 3 is the current Profile screen
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          label: 'Messages',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.favorite_border),
          label: 'favorite',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
      ],
    );
  }

  // Method to refresh stats when the profile screen is active
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh stats whenever the screen becomes active
    _loadUserStats();
  }

  ImageProvider? _getProfileImageProvider(String imageData) {
    // Check if it's a URL (starts with http or https)
    if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
      return NetworkImage(imageData);
    }

    // Try to decode as base64
    try {
      return MemoryImage(base64Decode(imageData));
    } catch (e) {
      _logger.e('Error decoding profile image: $e');
      return null;
    }
  }
}
