// This file handles imports that need to be conditional based on platform
// It helps avoid issues with libraries that are web-only or mobile-only

import 'package:flutter/foundation.dart';

/// Check if the app is running on the web platform
bool get isWeb => kIsWeb;

/// Initialize platform-specific libraries
void initializePlatformSpecificLibraries() {
  if (kIsWeb) {
    // Web-specific initialization
    // This is a no-op for now, but can be extended as needed
    debugPrint('Running on web platform - initializing web-specific libraries');
  } else {
    // Mobile-specific initialization
    debugPrint(
      'Running on mobile platform - initializing mobile-specific libraries',
    );
  }
}
