import 'package:firebase_storage/firebase_storage.dart';
import 'package:logger/logger.dart';

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Logger _logger = Logger();

  // Get a fresh download URL
  Future<String> getFreshDownloadUrl(String path) async {
    try {
      final ref = _storage.ref().child(path);
      return await ref.getDownloadURL();
    } catch (e) {
      _logger.e('Error getting download URL: $e');
      rethrow;
    }
  }

  // Extract path from a Firebase Storage URL
  String extractPathFromUrl(String url) {
    try {
      // Format: https://firebasestorage.googleapis.com/v0/b/BUCKET/o/PATH?token=TOKEN
      if (url.contains('/o/')) {
        final pathPart = url.split('/o/')[1].split('?')[0];
        return Uri.decodeFull(pathPart);
      }
    } catch (e) {
      _logger.e('Error extracting path from URL: $e');
    }
    return '';
  }

  // Check if a URL is a Firebase Storage URL
  bool isFirebaseStorageUrl(String url) {
    return url.contains('firebasestorage.googleapis.com') ||
        url.contains('storage.googleapis.com');
  }

  // Refresh a list of Firebase Storage URLs
  Future<List<String>> refreshStorageUrls(List<String> urls) async {
    final List<String> refreshedUrls = [];

    for (final url in urls) {
      try {
        if (isFirebaseStorageUrl(url)) {
          final path = extractPathFromUrl(url);
          if (path.isNotEmpty) {
            final freshUrl = await getFreshDownloadUrl(path);
            refreshedUrls.add(freshUrl);
          } else {
            refreshedUrls.add(url); // Add original if path extraction failed
          }
        } else {
          refreshedUrls.add(url); // Not a Firebase Storage URL
        }
      } catch (e) {
        _logger.e('Error refreshing URL $url: $e');
        refreshedUrls.add(url); // Keep original on error
      }
    }

    return refreshedUrls;
  }
}
