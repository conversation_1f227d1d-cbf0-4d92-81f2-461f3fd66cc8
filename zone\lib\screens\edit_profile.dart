import 'package:flutter/material.dart';
import '../firebase_auth.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  // ... (existing code)

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  final Auth _auth = Auth();
  bool _isLoading = false;

  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userData = await _auth.getUserData();
      if (!mounted) return;

      setState(() {
        _firstNameController.text = userData?['firstName'] ?? '';
        _lastNameController.text = userData?['lastName'] ?? '';
        _phoneController.text = userData?['phone'] ?? '';
        _addressController.text = userData?['address'] ?? '';

        // If profile image exists in userData, use it
        if (userData != null &&
            userData['profileImage'] != null &&
            userData['profileImage'].isNotEmpty) {
        }
      });
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load profile data: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Profile')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Profile editing UI goes here
                    // This is a placeholder implementation
                    const Text('Edit your profile information'),
                  ],
                ),
              ),
    );
  }

  // ... (rest of the existing code)
}
