import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../widgets/reusable_widget.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../screens/map_location_picker.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/firestore_collections.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../firebase_auth.dart'; // Import your custom Auth class

class AddListingScreen extends StatefulWidget {
  const AddListingScreen({super.key});

  @override
  State<AddListingScreen> createState() => _AddListingScreenState();
}

class _AddListingScreenState extends State<AddListingScreen> {
  final _formKey = GlobalKey<FormState>();
  final Logger _logger = Logger();
  final ImagePicker _picker = ImagePicker();
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final Auth _auth = Auth(); // Use custom Auth class
  final Uuid _uuid = const Uuid();

  bool _isLoading = false;
  final List<XFile> _selectedImages = [];

  // Form fields
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _bedroomsController = TextEditingController();
  final TextEditingController _bathroomsController = TextEditingController();
  final TextEditingController _areaController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  // Add these new fields for location
  final TextEditingController _latitudeController = TextEditingController();
  final TextEditingController _longitudeController = TextEditingController();
  LatLng? _selectedLocation;

  String _listingType = 'rent';
  String _propertyType = 'Apartment';
  String _status = 'available';

  // Property type options
  final List<String> _propertyTypes = ['Apartment', 'House', 'Land'];

  // Update the panoramic image variable to be a list
  final List<XFile> _panoramicImages = []; // Replace single XFile with List

  @override
  void dispose() {
    _titleController.dispose();
    _locationController.dispose();
    _priceController.dispose();
    _bedroomsController.dispose();
    _bathroomsController.dispose();
    _areaController.dispose();
    _descriptionController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  Future<List<String>> _uploadImagesToFirebase() async {
    List<String> imageUrls = [];

    try {
      for (var image in _selectedImages) {
        // Create a unique filename
        final String fileName =
            '${_uuid.v4()}_${kIsWeb ? 'image' : path.basename(image.path)}';

        // Create reference to the path in Firebase Storage
        final userId = _firebaseAuth.currentUser?.uid ?? 'anonymous';
        final storageRef = _storage.ref().child('listings/$userId/$fileName');

        // Upload the file
        late UploadTask uploadTask;
        if (kIsWeb) {
          // For web, use bytes from XFile directly
          final bytes = await image.readAsBytes();
          uploadTask = storageRef.putData(bytes);
        } else {
          // For mobile, use File
          uploadTask = storageRef.putFile(File(image.path));
        }

        // Wait for the upload to complete and get the download URL
        final snapshot = await uploadTask;
        final downloadUrl = await snapshot.ref.getDownloadURL();

        imageUrls.add(downloadUrl);
      }
      return imageUrls;
    } catch (e) {
      _logger.e('Error uploading images: $e');
      // Re-throw to be caught by the caller
      rethrow;
    }
  }

  Future<void> _pickLocation() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MapLocationPicker(initialLocation: _selectedLocation),
      ),
    );

    if (!mounted) return;

    if (result != null && result is LatLng) {
      setState(() {
        _selectedLocation = result;
        _latitudeController.text = result.latitude.toString();
        _longitudeController.text = result.longitude.toString();
      });
    }
  }

  // Update the _pick360Image method to handle multiple images
  Future<void> _pick360Image() async {
    try {
      if (_panoramicImages.length >= 3) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Maximum 3 panoramic images allowed')),
        );
        return;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 90,
      );

      if (!mounted) return;

      if (image != null) {
        setState(() {
          _panoramicImages.add(image);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('360° image ${_panoramicImages.length}/3 selected'),
          ),
        );
      }
    } catch (e) {
      _logger.e('Error picking 360° image: $e');
    }
  }

  // Update the upload method to handle multiple panoramic images
  Future<List<String>> _uploadPanoramicImages() async {
    if (_panoramicImages.isEmpty) return [];

    List<String> panoramaUrls = [];
    try {
      for (var image in _panoramicImages) {
        final String fileName =
            '${_uuid.v4()}_panorama${kIsWeb ? '' : path.extension(image.path)}';
        final userId = _firebaseAuth.currentUser?.uid ?? 'anonymous';
        final storageRef = _storage.ref().child('panoramas/$userId/$fileName');

        late UploadTask uploadTask;
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          uploadTask = storageRef.putData(bytes);
        } else {
          uploadTask = storageRef.putFile(File(image.path));
        }

        final snapshot = await uploadTask;
        final downloadUrl = await snapshot.ref.getDownloadURL();
        panoramaUrls.add(downloadUrl);
      }
      return panoramaUrls;
    } catch (e) {
      _logger.e('Error uploading panoramic images: $e');
      rethrow;
    }
  }

  Future<void> _submitForm() async {
    // Check auth status
    if (_firebaseAuth.currentUser == null) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You must be logged in to create a listing'),
          backgroundColor: Colors.red,
        ),
      );
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    // Check user role
    final userRole = await _auth.getUserRole();
    if (userRole != 'Seller' && userRole != 'Landlord') {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only sellers and landlords can create listings'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Upload regular images
        final List<String> imageUrls = [];
        if (_selectedImages.isNotEmpty) {
          imageUrls.addAll(await _uploadImagesToFirebase());
        }

        // Upload panoramic images
        final List<String> panoramaUrls = await _uploadPanoramicImages();

        // Get current user information
        final currentUser = _firebaseAuth.currentUser;
        final userDoc =
            await FirebaseFirestore.instance
                .collection(FirestoreCollections.users)
                .doc(currentUser?.uid)
                .get();

        final userName =
            userDoc.data()?['firstName'] != null &&
                    userDoc.data()?['lastName'] != null
                ? "${userDoc.data()?['firstName']} ${userDoc.data()?['lastName']}"
                : "Unknown User";

        // Create a map of listing data
        final listingData = {
          'title': _titleController.text,
          'location': _locationController.text,
          'price': double.tryParse(_priceController.text) ?? 0,
          'listingType': _listingType,
          'propertyType': _propertyType,
          'bedrooms': int.tryParse(_bedroomsController.text),
          'bathrooms': int.tryParse(_bathroomsController.text),
          'area': double.tryParse(_areaController.text),
          'description': _descriptionController.text,
          'status': _status,
          'images': imageUrls,
          'panoramaUrls': panoramaUrls, // Update to store multiple URLs
          'hasPanorama': panoramaUrls.isNotEmpty,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
          'userId': currentUser?.uid,
          'userName': userName,
          'userEmail': currentUser?.email,
          // Add location data
          'latitude': _selectedLocation?.latitude,
          'longitude': _selectedLocation?.longitude,
          // Add verification status
          'verificationStatus': 'pending',
          'isVerified': false,
          'verificationDate': null,
          'adminFeedback': null,
        };

        // Log the data for debugging
        _logger.d('Listing data prepared: $listingData');

        // Add document to Firestore
        final docRef = await FirebaseFirestore.instance
            .collection(FirestoreCollections.properties)
            .add(listingData);

        // Create a notification for admin
        await FirebaseFirestore.instance.collection('notifications').add({
          'type': 'verification_request',
          'propertyId': docRef.id,
          'title': _titleController.text,
          'sellerName': userName,
          'sellerId': currentUser?.uid,
          'timestamp': FieldValue.serverTimestamp(),
          'read': false,
          'status': 'pending',
        });

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Listing created successfully and sent for admin verification',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to listings
        Navigator.pop(context);
      } catch (e) {
        _logger.e('Error creating listing: $e');

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating listing: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // Update the panorama section UI in the build method
  Widget _build360Section() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '360° Panoramic Views',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              '${_panoramicImages.length}/3',
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          'Add up to 3 panoramic images for a virtual tour',
          style: TextStyle(color: Colors.grey),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount:
              _panoramicImages.length + (_panoramicImages.length < 3 ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _panoramicImages.length) {
              return GestureDetector(
                onTap: _pick360Image,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.panorama, size: 32),
                      SizedBox(height: 4),
                      Text('Add 360°', textAlign: TextAlign.center),
                    ],
                  ),
                ),
              );
            }

            return Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child:
                        kIsWeb
                            ? Image.network(
                              _panoramicImages[index].path,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            )
                            : Image.file(
                              File(_panoramicImages[index].path),
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _panoramicImages.removeAt(index);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 16,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
                const Positioned(
                  bottom: 4,
                  right: 4,
                  child: Icon(Icons.panorama, color: Colors.white, size: 20),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Add New Listing'),
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'Create a new property listing for rent or sale',
                    style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                  ),
                ),
                const SizedBox(height: 20),

                // Basic Information Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Basic Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        CustomTextFormField(
                          labelText: 'Listing Title',
                          controller: _titleController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        CustomTextFormField(
                          labelText: 'Location',
                          controller: _locationController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a location';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text('Listing Type'),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: RadioListTile<String>(
                                          title: const Text('For Rent'),
                                          value: 'rent',
                                          groupValue: _listingType,
                                          onChanged: (value) {
                                            setState(() {
                                              _listingType = value!;
                                            });
                                          },
                                          contentPadding: EdgeInsets.zero,
                                          dense: true,
                                        ),
                                      ),
                                      Expanded(
                                        child: RadioListTile<String>(
                                          title: const Text('For Sale'),
                                          value: 'sale',
                                          groupValue: _listingType,
                                          onChanged: (value) {
                                            setState(() {
                                              _listingType = value!;
                                            });
                                          },
                                          contentPadding: EdgeInsets.zero,
                                          dense: true,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Property Type',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: Colors.lightBlue[300]!,
                              ),
                            ),
                          ),
                          value: _propertyType,
                          items:
                              _propertyTypes.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              _propertyType = newValue!;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        CustomTextFormField(
                          labelText: 'Price (₱)',
                          controller: _priceController,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a price';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                // Photos Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Property Photos',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Add photos of your property',
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                        const SizedBox(height: 16),
                        // Photo grid
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                              ),
                          itemCount: _selectedImages.length + 1,
                          itemBuilder: (context, index) {
                            if (index == _selectedImages.length) {
                              // Add photo button
                              return GestureDetector(
                                onTap: () async {
                                  if (_selectedImages.length >= 10) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Maximum 10 photos allowed',
                                        ),
                                      ),
                                    );
                                    return;
                                  }
                                  final XFile? image = await _picker.pickImage(
                                    source: ImageSource.gallery,
                                    imageQuality: 80,
                                  );
                                  if (image != null) {
                                    setState(() {
                                      _selectedImages.add(image);
                                    });
                                  }
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.grey[300]!,
                                    ),
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.add_photo_alternate_outlined,
                                      size: 40,
                                    ),
                                  ),
                                ),
                              );
                            }
                            // Image preview
                            return Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.grey[300]!,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child:
                                        kIsWeb
                                            ? Image.network(
                                              _selectedImages[index].path,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            )
                                            : Image.file(
                                              File(_selectedImages[index].path),
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            ),
                                  ),
                                ),
                                // Remove button
                                Positioned(
                                  top: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedImages.removeAt(index);
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: const BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        size: 16,
                                        color: Colors.red,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        if (_selectedImages.isEmpty) ...[
                          const SizedBox(height: 16),
                          Center(
                            child: Text(
                              'Add at least one photo of your property',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Property Details Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Property Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: CustomTextFormField(
                                labelText: 'Bedrooms',
                                controller: _bedroomsController,
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: CustomTextFormField(
                                labelText: 'Bathrooms',
                                controller: _bathroomsController,
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        CustomTextFormField(
                          labelText: 'Area (sq.ft)',
                          controller: _areaController,
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 16),
                        CustomTextFormField(
                          labelText: 'Description',
                          controller: _descriptionController,
                          keyboardType: TextInputType.multiline,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a description';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                // Location Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Location',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Address field
                        CustomTextFormField(
                          labelText: 'Address',
                          controller: _locationController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter an address';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Map coordinates field with pick on map button
                        Row(
                          children: [
                            Expanded(
                              child: CustomTextFormField(
                                labelText: 'Latitude',
                                controller: _latitudeController,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                      decimal: true,
                                    ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: CustomTextFormField(
                                labelText: 'Longitude',
                                controller: _longitudeController,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                      decimal: true,
                                    ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Pick on map button
                        Center(
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.map),
                            label: const Text('Pick location on map'),
                            onPressed: _pickLocation,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 12,
                              ),
                            ),
                          ),
                        ),

                        // Map preview if location is selected
                        if (_selectedLocation != null) ...[
                          const SizedBox(height: 16),
                          Container(
                            height: 200,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: GoogleMap(
                                initialCameraPosition: CameraPosition(
                                  target: _selectedLocation!,
                                  zoom: 15,
                                ),
                                markers: {
                                  Marker(
                                    markerId: const MarkerId(
                                      'selected_location',
                                    ),
                                    position: _selectedLocation!,
                                  ),
                                },
                                liteModeEnabled: true,
                                myLocationButtonEnabled: false,
                                zoomControlsEnabled: false,
                                mapToolbarEnabled: false,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Photos Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Virtual Tour',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // 360° Panorama section
                        _build360Section(),
                      ],
                    ),
                  ),
                ),

                // Availability Card
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Availability',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          value: _status,
                          items: const [
                            DropdownMenuItem(
                              value: 'available',
                              child: Text('Available Now'),
                            ),
                            DropdownMenuItem(
                              value: 'coming_soon',
                              child: Text('Coming Soon'),
                            ),
                            DropdownMenuItem(
                              value: 'draft',
                              child: Text('Save as Draft'),
                            ),
                          ],
                          onChanged: (String? newValue) {
                            setState(() {
                              _status = newValue!;
                            });
                          },
                        ),
                        const SizedBox(height: 24),
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed:
                                    _isLoading
                                        ? null
                                        : () => Navigator.pop(context),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                ),
                                child: const Text('Cancel'),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: CustomButton(
                                text:
                                    _isLoading
                                        ? 'Creating...'
                                        : 'Create Listing',
                                onPressed: _isLoading ? () {} : _submitForm,
                                height: 48,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
