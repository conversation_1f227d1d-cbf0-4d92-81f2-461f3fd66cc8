import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import '../models/firestore_collections.dart';
import 'add_listing.dart';
import 'edit_listing.dart';

class SellerListingsScreen extends StatefulWidget {
  const SellerListingsScreen({super.key});

  @override
  State<SellerListingsScreen> createState() => _SellerListingsScreenState();
}

class _SellerListingsScreenState extends State<SellerListingsScreen> {
  final Logger _logger = Logger();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  bool _isLoading = true;
  List<DocumentSnapshot> _listings = [];
  String _filterStatus = 'Approved'; // All, Approved, Pending, Rejected

  @override
  void initState() {
    super.initState();
    _loadListings();
  }

  Future<void> _loadListings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Query the user's listings
      final QuerySnapshot snapshot =
          await _firestore
              .collection(FirestoreCollections.properties)
              .where('userId', isEqualTo: userId)
              .orderBy('createdAt', descending: true)
              .get();

      setState(() {
        _listings = snapshot.docs;
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('Error loading listings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<DocumentSnapshot> get _filteredListings {
    if (_filterStatus == 'All') {
      return _listings;
    }

    final String statusFilter = _filterStatus.toLowerCase();
    return _listings.where((doc) {
      final data = doc.data() as Map<String, dynamic>;
      if (statusFilter == 'approved') {
        return data['verificationStatus'] == 'approved';
      } else if (statusFilter == 'pending') {
        return data['verificationStatus'] == 'pending';
      } else if (statusFilter == 'rejected') {
        return data['verificationStatus'] == 'rejected';
      }
      return false;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Properties'),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _filterStatus = value;
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'All',
                    child: Text('All Properties'),
                  ),
                  const PopupMenuItem(
                    value: 'Approved',
                    child: Text('Approved Only'),
                  ),
                  const PopupMenuItem(
                    value: 'Pending',
                    child: Text('Pending Approval'),
                  ),
                  const PopupMenuItem(
                    value: 'Rejected',
                    child: Text('Rejected'),
                  ),
                ],
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredListings.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.home_work_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _filterStatus == 'All'
                          ? 'No properties found'
                          : 'No $_filterStatus properties found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Your properties will appear here',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AddListingScreen(),
                          ),
                        ).then((_) => _loadListings());
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Add New Property'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              )
              : RefreshIndicator(
                onRefresh: _loadListings,
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredListings.length,
                  itemBuilder: (context, index) {
                    final doc = _filteredListings[index];
                    final property = doc.data() as Map<String, dynamic>;

                    // Extract property details
                    final String title = property['title'] ?? 'No Title';
                    final String location =
                        property['location'] ?? 'No Location';
                    final double price =
                        property['price'] is double
                            ? property['price']
                            : property['price'] is int
                            ? (property['price'] as int).toDouble()
                            : 0.0;
                    final String status =
                        property['verificationStatus'] ?? 'pending';
                    final List<dynamic> images = property['images'] ?? [];

                    // Get status color
                    Color statusColor;
                    IconData statusIcon;
                    if (status == 'approved') {
                      statusColor = Colors.green;
                      statusIcon = Icons.check_circle;
                    } else if (status == 'rejected') {
                      statusColor = Colors.red;
                      statusIcon = Icons.cancel;
                    } else {
                      statusColor = Colors.orange;
                      statusIcon = Icons.hourglass_empty;
                    }

                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: InkWell(
                        onTap: () {
                          // Navigate to property details or edit screen
                          if (status == 'approved') {
                            // Show options: Edit or View
                            showModalBottomSheet(
                              context: context,
                              builder:
                                  (context) => Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ListTile(
                                        leading: const Icon(Icons.edit),
                                        title: const Text('Edit Listing'),
                                        onTap: () {
                                          Navigator.pop(context);
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      EditListingScreen(
                                                        propertyId: doc.id,
                                                        propertyData: property,
                                                      ),
                                            ),
                                          ).then((_) => _loadListings());
                                        },
                                      ),
                                      ListTile(
                                        leading: const Icon(
                                          Icons.remove_circle,
                                        ),
                                        title: const Text('Remove Listing'),
                                        onTap: () {
                                          Navigator.pop(context);
                                          // Show confirmation dialog
                                          showDialog(
                                            context: context,
                                            builder:
                                                (context) => AlertDialog(
                                                  title: const Text(
                                                    'Remove Listing',
                                                  ),
                                                  content: const Text(
                                                    'Are you sure you want to remove this listing?',
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed:
                                                          () => Navigator.pop(
                                                            context,
                                                          ),
                                                      child: const Text(
                                                        'Cancel',
                                                      ),
                                                    ),
                                                    TextButton(
                                                      onPressed: () async {
                                                        Navigator.pop(context);
                                                        // Store scaffold messenger before async gap
                                                        final scaffoldMessenger =
                                                            ScaffoldMessenger.of(
                                                              context,
                                                            );

                                                        // Delete the listing
                                                        try {
                                                          await _firestore
                                                              .collection(
                                                                FirestoreCollections
                                                                    .properties,
                                                              )
                                                              .doc(doc.id)
                                                              .delete();
                                                          _loadListings();
                                                          if (mounted) {
                                                            scaffoldMessenger
                                                                .showSnackBar(
                                                                  const SnackBar(
                                                                    content: Text(
                                                                      'Listing removed',
                                                                    ),
                                                                  ),
                                                                );
                                                          }
                                                        } catch (e) {
                                                          _logger.e(
                                                            'Error removing listing: $e',
                                                          );
                                                          if (mounted) {
                                                            scaffoldMessenger
                                                                .showSnackBar(
                                                                  SnackBar(
                                                                    content: Text(
                                                                      'Error: $e',
                                                                    ),
                                                                  ),
                                                                );
                                                          }
                                                        }
                                                      },
                                                      child: const Text(
                                                        'Remove',
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                            );
                          } else {
                            // Just show status message for non-approved listings
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  status == 'pending'
                                      ? 'This listing is pending approval'
                                      : 'This listing was rejected',
                                ),
                              ),
                            );
                          }
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Property image
                            Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    topRight: Radius.circular(12),
                                  ),
                                  child: SizedBox(
                                    height: 180,
                                    width: double.infinity,
                                    child:
                                        images.isNotEmpty
                                            ? Image.network(
                                              images[0],
                                              fit: BoxFit.cover,
                                            )
                                            : Container(
                                              color: Colors.grey[300],
                                              child: const Icon(
                                                Icons.home,
                                                size: 50,
                                                color: Colors.white,
                                              ),
                                            ),
                                  ),
                                ),
                                // Status indicator
                                Positioned(
                                  top: 16,
                                  right: 16,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: statusColor.withValues(
                                        red: statusColor.r.toDouble(),
                                        green: statusColor.g.toDouble(),
                                        blue: statusColor.b.toDouble(),
                                        alpha: (0.8 * 255).round().toDouble(),
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          statusIcon,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          status.toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.black,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // Property details
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    title,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.location_on,
                                        size: 16,
                                        color: Colors.grey,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          location,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            '\$${price.toStringAsFixed(2)}',
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue,
                                            ),
                                          ),
                                          Text(
                                            property['listingType'] == 'rent'
                                                ? '/month'
                                                : '',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (status == 'approved')
                                        const Icon(
                                          Icons.arrow_forward_ios,
                                          size: 16,
                                          color: Colors.grey,
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
    );
  }
}
