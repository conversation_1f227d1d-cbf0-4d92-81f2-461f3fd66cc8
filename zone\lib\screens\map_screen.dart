import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../utils/map_utils.dart';

class PropertyMapScreen extends StatefulWidget {
  final List<Map<String, dynamic>>? properties;

  const PropertyMapScreen({super.key, this.properties});

  @override
  State<PropertyMapScreen> createState() => _PropertyMapScreenState();
}

class _PropertyMapScreenState extends State<PropertyMapScreen> {
  GoogleMapController? _mapController;
  final Logger _logger = Logger();
  Set<Marker> _markers = {};
  bool _isLoading = true;
  bool _mapInitialized = false;

  // Default camera position (Philippines)
  static const CameraPosition _defaultLocation = CameraPosition(
    target: LatLng(14.5995, 120.9842), // Manila, Philippines
    zoom: 11.0,
  );

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    try {
      // Initialize maps if needed
      if (kIsWeb) {
        await MapUtils.initialize();
      }

      if (widget.properties != null) {
        _setMarkers(widget.properties!);
      } else {
        await _loadProperties();
      }

      setState(() {
        _mapInitialized = true;
      });
    } catch (e) {
      _logger.e('Error initializing map: $e');
      setState(() {
        _isLoading = false;
        _mapInitialized = false;
      });
    }
  }

  Future<void> _loadProperties() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final QuerySnapshot snapshot =
          await FirebaseFirestore.instance.collection('properties').get();

      final List<Map<String, dynamic>> properties =
          snapshot.docs
              .map(
                (doc) => {...doc.data() as Map<String, dynamic>, 'id': doc.id},
              )
              .toList();

      _setMarkers(properties);
    } catch (e) {
      _logger.e('Error loading properties: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _setMarkers(List<Map<String, dynamic>> properties) {
    final Set<Marker> markers = {};

    for (final property in properties) {
      // Skip if property doesn't have location data
      if (!property.containsKey('latitude') ||
          !property.containsKey('longitude')) {
        continue;
      }

      final double? latitude =
          property['latitude'] is double
              ? property['latitude']
              : double.tryParse(property['latitude'].toString());

      final double? longitude =
          property['longitude'] is double
              ? property['longitude']
              : double.tryParse(property['longitude'].toString());

      if (latitude == null || longitude == null) continue;

      markers.add(
        Marker(
          markerId: MarkerId(property['id'].toString()),
          position: LatLng(latitude, longitude),
          infoWindow: InfoWindow(
            title: property['title'] ?? 'Property',
            snippet:
                '₱${property['price']?.toString() ?? 'Price not available'}',
          ),
          onTap: () => _onMarkerTapped(property),
        ),
      );
    }

    setState(() {
      _markers = markers;
      _isLoading = false;
    });

    // Move camera to first property if available
    if (markers.isNotEmpty && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(markers.first.position, 12),
      );
    }
  }

  void _onMarkerTapped(Map<String, dynamic> property) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => PropertyInfoCard(property: property),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Property Map'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: widget.properties == null ? _loadProperties : null,
          ),
        ],
      ),
      body: Stack(
        children: [
          // Show error message if map failed to initialize
          if (!_mapInitialized && !_isLoading)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.location_off, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Could not load the map',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _initializeMap,
                    child: Text('Try Again'),
                  ),
                ],
              ),
            ),

          // Show map if initialized
          if (_mapInitialized)
            GoogleMap(
              initialCameraPosition: _defaultLocation,
              markers: _markers,
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              mapToolbarEnabled: true,
              onMapCreated: (controller) {
                _mapController = controller;
                // Apply custom map style here if needed
              },
            ),

          // Show loading indicator if loading
          if (_isLoading) const Center(child: CircularProgressIndicator()),
        ],
      ),
      floatingActionButton:
          _mapInitialized
              ? FloatingActionButton(
                onPressed:
                    () => _mapController?.animateCamera(
                      CameraUpdate.newCameraPosition(_defaultLocation),
                    ),
                child: const Icon(Icons.center_focus_strong),
              )
              : null,
    );
  }
}

class PropertyInfoCard extends StatelessWidget {
  final Map<String, dynamic> property;

  const PropertyInfoCard({super.key, required this.property});

  @override
  Widget build(BuildContext context) {
    final String title = property['title'] ?? 'Property';
    final String location = property['location'] ?? 'No location provided';
    final String type = property['propertyType'] ?? 'Property';
    final String priceText =
        property['price'] != null
            ? '₱${property['price'].toString()}'
            : 'Price not available';
    final String? imageUrl =
        property['images'] != null && (property['images'] as List).isNotEmpty
            ? property['images'][0]
            : null;

    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                    image:
                        imageUrl != null
                            ? DecorationImage(
                              image: NetworkImage(imageUrl),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      imageUrl == null
                          ? const Icon(Icons.home, size: 40, color: Colors.grey)
                          : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              location,
                              style: TextStyle(color: Colors.grey[600]),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              type,
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            priceText,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text('Description', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(
              property['description'] ?? 'No description available',
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPropertyDetail(
                  Icons.bed,
                  property['bedrooms']?.toString() ?? 'N/A',
                  'Beds',
                ),
                _buildPropertyDetail(
                  Icons.bathtub_outlined,
                  property['bathrooms']?.toString() ?? 'N/A',
                  'Baths',
                ),
                _buildPropertyDetail(
                  Icons.square_foot,
                  property['area'] != null ? '${property['area']} sqft' : 'N/A',
                  'Area',
                ),
              ],
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: () {
                  // Navigate to property details
                  Navigator.pop(context);
                  // Add navigation to detailed property view
                },
                child: const Text('View Details'),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyDetail(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
      ],
    );
  }
}
