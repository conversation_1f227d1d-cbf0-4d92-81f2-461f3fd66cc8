// Web-specific map utilities
import 'package:web/web.dart' as web;
import 'package:js/js.dart';
import 'package:flutter/foundation.dart';

// Define the JavaScript function for checking Google Maps
@JS('window.google')
external Object? get _google;

@JS('window.google.maps')
external Object? get _googleMaps;

/// Initialize web-specific map functionality
Future<void> initializeWebMaps() async {
  if (kIsWeb) {
    try {
      // Check if Google Maps API is loaded
      if (_isGoogleMapsLoaded()) {
        debugPrint('Google Maps API is loaded successfully');
      } else {
        debugPrint('Warning: Google Maps API not detected in the document');
        // You could inject the script here as a fallback, but it's better
        // to load it in index.html to ensure it's available before Flutter initializes
      }
    } catch (e) {
      debugPrint('Error initializing Google Maps: $e');
    }
  }
}

/// Check if the Google Maps API is loaded in the browser
bool _isGoogleMapsLoaded() {
  try {
    // Check if script tag is in document
    final hasScriptTag =
        web.document.querySelector('script[src*="maps.googleapis.com"]') !=
        null;

    // Check if the global 'google' object exists with maps property
    final hasGoogleMaps = _google != null && _googleMaps != null;

    return hasScriptTag && hasGoogleMaps;
  } catch (e) {
    debugPrint('Error checking Google Maps: $e');
    return false;
  }
}
