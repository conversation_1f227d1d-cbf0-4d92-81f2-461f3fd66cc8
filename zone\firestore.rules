rules_version = '2';

// Firestore Rules
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper function to check basic authentication
    function isAuthenticated() {
      return request.auth != null;
    }

    // Default deny all
    match /{document=**} {
      allow read, write: if false;
    }

    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (request.auth.uid == userId || isAdmin());
    }

    // Properties collection
    match /properties/{propertyId} {
      allow read: if true; // Anyone can read properties
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() &&
                    (resource.data.userId == request.auth.uid || 
                     (isAdmin() && 
                      (request.resource.data.diff(resource.data).affectedKeys()
                       .hasOnly(['isVerified', 'verificationStatus', 'verificationDate', 'adminFeedback']))));
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Conversations collection
    match /conversations/{conversationId} {
      // Allow read if user is a participant in the conversation
      allow read: if isAuthenticated() && 
                   (request.auth.uid in resource.data.participants || 
                    request.auth.uid == resource.data.agentId ||
                    request.auth.uid == resource.data.customerId ||
                    request.auth.uid == resource.data.userId);
      
      // Allow create with proper validation            
      allow create: if isAuthenticated() && 
                     (request.auth.uid in request.resource.data.participants ||
                      request.auth.uid == request.resource.data.agentId ||
                      request.auth.uid == request.resource.data.customerId ||
                      request.auth.uid == request.resource.data.userId);
      
      // Allow update if user is a participant and only changing allowed fields
      allow update: if isAuthenticated() && 
                     (request.auth.uid in resource.data.participants || 
                      request.auth.uid == resource.data.agentId ||
                      request.auth.uid == resource.data.customerId ||
                      request.auth.uid == resource.data.userId);

      // Messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated() && 
                     (request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants ||
                      request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.agentId ||
                      request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.customerId ||
                      request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId ||
                      request.auth.uid == resource.data.senderId ||
                      request.auth.uid == resource.data.recipientId);
                      
        allow create: if isAuthenticated() && 
                       (request.resource.data.senderId == request.auth.uid &&
                       (request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.agentId ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.customerId ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId));
                        
        allow update: if isAuthenticated() &&
                       (request.auth.uid == resource.data.senderId ||
                        request.auth.uid == resource.data.recipientId ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.agentId ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.customerId ||
                        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId);
      }
    }

    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() &&
                            (isAdmin() || resource.data.sellerId == request.auth.uid);
    }

    // User Notifications collection
    match /user_notifications/{notificationId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && (isAdmin() || request.resource.data.userId == request.auth.uid);
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Favorites collection
    match /favorites/{favoriteId} {
      allow read: if isAuthenticated() && 
                  (resource.data.userId == request.auth.uid || 
                   request.query.limit <= 100 && request.query.filters.userId == request.auth.uid);
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }
    
    // Views collection
    match /views/{viewId} {
      allow read: if isAuthenticated() && 
                  (resource.data.userId == request.auth.uid || 
                   request.query.limit <= 100 && request.query.filters.userId == request.auth.uid);
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }
  }
}

// Firebase Storage Rules
service firebase.storage {
  match /b/{bucket}/o {

    // Panoramas folder
    match /panoramas/{userId}/{fileName} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Listings folder
    match /listings/{userId}/{fileName} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
} 