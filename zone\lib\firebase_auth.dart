import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'models/firestore_collections.dart';

class Auth {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? get currentUser => _firebaseAuth.currentUser;

  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    final UserCredential userCredential = await _firebaseAuth
        .signInWithEmailAndPassword(email: email, password: password);

    return userCredential;
  }

  Future<void> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? role,
    String? firstName,
    String? lastName,
    String? address,
    String? contact,
    String? username,
  }) async {
    // Create the user with Firebase Auth
    UserCredential userCredential = await _firebaseAuth
        .createUserWithEmailAndPassword(email: email, password: password);

    // Save additional user data to Firestore
    if (userCredential.user != null && role != null) {
      await _firestore
          .collection(FirestoreCollections.users)
          .doc(userCredential.user!.uid)
          .set({
            'email': email,
            'role': role,
            'firstName': firstName ?? '',
            'lastName': lastName ?? '',
            'address': address ?? '',
            'contact': contact ?? '',
            'username': username ?? email.split('@')[0],
            'profileImage':
                '', // Empty string for now, will be updated when they add a photo
            'createdAt': FieldValue.serverTimestamp(),
          });
    }
  }

  // Get user role from Firestore
  Future<String?> getUserRole() async {
    if (currentUser == null) return null;

    try {
      DocumentSnapshot doc =
          await _firestore
              .collection(FirestoreCollections.users)
              .doc(currentUser!.uid)
              .get();

      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return data['role'] as String?;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // Get full user data from Firestore
  Future<Map<String, dynamic>?> getUserData() async {
    if (currentUser == null) return null;

    try {
      DocumentSnapshot doc =
          await _firestore
              .collection(FirestoreCollections.users)
              .doc(currentUser!.uid)
              .get();

      if (doc.exists) {
        return doc.data() as Map<String, dynamic>;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // Update user profile data with improved error handling
  Future<void> updateUserProfile({
    String? firstName,
    String? lastName,
    String? address,
    String? contact,
    String? username,
    String? profileImage,
  }) async {
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    try {
      final Map<String, dynamic> updates = {};

      if (firstName != null) updates['firstName'] = firstName;
      if (lastName != null) updates['lastName'] = lastName;
      if (address != null) updates['address'] = address;
      if (contact != null) updates['contact'] = contact;
      if (username != null) updates['username'] = username;

      // Make sure profile image is valid if provided
      if (profileImage != null) {
        // Validate profile image isn't too large
        if (profileImage.length > 1000000) {
          throw Exception(
            'Profile image is too large. Please use a smaller image.',
          );
        }
        updates['profileImage'] = profileImage;
      }

      if (updates.isEmpty) {
        throw Exception('No updates provided');
      }

      // Add timestamp to track when profile was last updated
      updates['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(FirestoreCollections.users)
          .doc(currentUser!.uid)
          .update(updates);

      return;
    } on FirebaseException catch (e) {
      throw Exception('Firebase error: ${e.message}');
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  Future<void> createAdmin(String email, String password) async {
    // Create the user with Firebase Auth
    UserCredential userCredential = await _firebaseAuth
        .createUserWithEmailAndPassword(email: email, password: password);

    // Save admin data to Firestore
    if (userCredential.user != null) {
      await _firestore
          .collection(FirestoreCollections.users)
          .doc(userCredential.user!.uid)
          .set({
            'email': email,
            'role': 'admin',
            'createdAt': FieldValue.serverTimestamp(),
          });
    }
  }

  Future<bool> isAdmin(String uid) async {
    final doc =
        await _firestore.collection(FirestoreCollections.users).doc(uid).get();
    return doc.exists && doc.data()?['role'] == 'admin';
  }
}
