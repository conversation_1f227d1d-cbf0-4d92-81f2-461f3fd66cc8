import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/firestore_collections.dart';
import 'property_detail_screen.dart';

class AllPropertiesScreen extends StatefulWidget {
  final String title;
  final bool isFeatured;

  const AllPropertiesScreen({
    super.key,
    required this.title,
    this.isFeatured = false,
  });

  @override
  State<AllPropertiesScreen> createState() => _AllPropertiesScreenState();
}

class _AllPropertiesScreenState extends State<AllPropertiesScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _properties = [];

  @override
  void initState() {
    super.initState();
    _loadProperties();
  }

  Future<void> _loadProperties() async {
    setState(() {
      _isLoading = true;
    });

    try {
      Query query = FirebaseFirestore.instance
          .collection(FirestoreCollections.properties)
          .where('isVerified', isEqualTo: true)
          .where('verificationStatus', isEqualTo: 'approved');

      if (widget.isFeatured) {
        // Add any featured property criteria here
        query = query.orderBy('createdAt', descending: true).limit(10);
      }

      final QuerySnapshot snapshot = await query.get();

      setState(() {
        _properties =
            snapshot.docs.map((doc) {
              final data = doc.data() as Map<String, dynamic>;
              return {...data, 'id': doc.id};
            }).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title)),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _properties.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.home_work_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No properties found',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _properties.length,
                itemBuilder: (context, index) {
                  final property = _properties[index];
                  return _buildPropertyCard(property);
                },
              ),
    );
  }

  Widget _buildPropertyCard(Map<String, dynamic> property) {
    final String title = property['title'] ?? 'No Title';
    final String location = property['location'] ?? 'No Location';
    final double price =
        property['price'] is double
            ? property['price']
            : property['price'] is int
            ? (property['price'] as int).toDouble()
            : 0.0;
    final List<dynamic> images = property['images'] ?? [];
    final bool hasPanorama = property['hasPanorama'] ?? false;
    final String listingType = property['listingType'] ?? 'sale';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PropertyDetailScreen(property: property),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(4),
                      ),
                      image:
                          images.isNotEmpty
                              ? DecorationImage(
                                image: NetworkImage(images[0]),
                                fit: BoxFit.cover,
                              )
                              : null,
                    ),
                    child:
                        images.isEmpty
                            ? const Icon(
                              Icons.home,
                              size: 50,
                              color: Colors.white,
                            )
                            : null,
                  ),
                ),
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          listingType == 'rent'
                              ? Colors.green.withValues(
                                red: 76,
                                green: 175,
                                blue: 80,
                                alpha: 230,
                              )
                              : Colors.blue.withValues(
                                red: 33,
                                green: 150,
                                blue: 243,
                                alpha: 230,
                              ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      listingType == 'rent' ? 'For Rent' : 'For Sale',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                if (hasPanorama)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(
                          red: 33,
                          green: 150,
                          blue: 243,
                          alpha: 230,
                        ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.view_in_ar, color: Colors.white, size: 16),
                          SizedBox(width: 4),
                          Text(
                            '360°',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          location,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₱${price.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      Text(
                        property['listingType'] == 'rent' ? '/month' : '',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
